# 漫画阅读器 - 双模式功能说明

## 功能概述

漫画阅读器现在支持两种查看模式，用户可以根据个人喜好和阅读习惯自由切换：

### 模式1：分页模式（Page Mode）
- **特点**：一次显示一页漫画图片
- **操作方式**：
  - 点击图片或使用"下一页"按钮翻页
  - 键盘左右箭头键翻页
  - 触摸设备上支持左右滑动翻页
- **适用场景**：适合专注阅读单页内容，模拟传统翻页体验

### 模式2：滚动模式（Scroll Mode）- 默认模式
- **特点**：将整个章节的所有图片垂直拼接显示
- **操作方式**：
  - 上下滚动查看所有页面
  - 使用"回到顶部"和"跳到底部"按钮快速导航
- **适用场景**：适合连续阅读，一次性浏览整个章节

## 操作指南

### 模式切换
1. **按钮切换**：点击顶部控制栏右侧的模式切换按钮
   - 在分页模式时显示"滚动模式"按钮
   - 在滚动模式时显示"分页模式"按钮

2. **快捷键切换**：按键盘 `V` 键快速切换模式

### 分页模式操作
- **翻页方式**：
  - 点击图片区域：下一页
  - 左右箭头键：上一页/下一页
  - 触摸滑动：向右滑动=上一页，向左滑动=下一页
  - 底部控制按钮：上一页/下一页

- **章节导航**：
  - 上一章/下一章按钮
  - 页面信息显示：当前页/总页数

### 滚动模式操作
- **滚动方式**：
  - 鼠标滚轮或触摸滑动
  - 键盘上下箭头键
  - 底部"回到顶部"/"跳到底部"按钮

- **章节导航**：
  - 上一章/下一章按钮
  - 页面信息显示：共X页

### 通用功能
- **控制栏显示/隐藏**：点击阅读区域任意位置
- **返回功能**：顶部"返回"按钮或按 `ESC` 键
- **回到首页**：顶部"首页"按钮

## 技术特性

### 响应式设计
- 自适应不同屏幕尺寸
- 移动设备触摸优化
- 平滑滚动和过渡效果

### 性能优化
- 图片懒加载（滚动模式）
- 错误处理和占位图
- 流畅的动画效果

### 用户体验
- 模式切换时的提示信息
- 键盘快捷键支持
- 触摸手势识别
- 自动隐藏控制栏

## 默认设置

- **默认模式**：滚动模式
- **控制栏**：3秒后自动隐藏
- **图片适配**：自动适应屏幕尺寸
- **滑动阈值**：50像素（分页模式触摸滑动）

## 使用建议

1. **首次使用**：建议先尝试默认的滚动模式，体验连续阅读
2. **精细阅读**：切换到分页模式，逐页仔细查看
3. **移动设备**：充分利用触摸滑动功能
4. **快速导航**：使用快捷键提高操作效率

这种双模式设计兼顾了不同用户的阅读习惯，提供了更加灵活和个性化的漫画阅读体验。

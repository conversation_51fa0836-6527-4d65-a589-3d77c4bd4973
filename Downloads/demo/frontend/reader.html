<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>漫画阅读器</title>
  <!-- ElementUI CSS -->
  <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    }
    .reader-container {
      background: #000;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }
    .reader-header {
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      transition: transform 0.3s;
    }
    .reader-header.hidden {
      transform: translateY(-100%);
    }
    .reader-content {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-top: 60px;
      min-height: calc(100vh - 120px);
    }
    .comic-page {
      max-width: 100%;
      max-height: 90vh;
      object-fit: contain;
      cursor: pointer;
    }
    .reader-controls {
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      transition: transform 0.3s;
    }
    .reader-controls.hidden {
      transform: translateY(100%);
    }
    .page-info {
      font-size: 14px;
    }
    .controls-left, .controls-right {
      display: flex;
      gap: 10px;
    }
    .header-left, .header-right {
      display: flex;
      align-items: center;
    }
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      color: white;
    }
    .error {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100vh;
      color: white;
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="reader-container" @click="toggleControls">
      <!-- 顶部控制栏 -->
      <div class="reader-header" :class="{ hidden: !showControls }">
        <div class="header-left">
          <el-button @click="goBack" icon="el-icon-arrow-left" type="text" style="color: white;">
            返回
          </el-button>
          <span style="color: white; margin-left: 10px;">{{ chapterTitle }}</span>
        </div>
        <div class="header-right">
          <el-button @click="goHome" type="text" style="color: white;">
            <i class="el-icon-house"></i> 首页
          </el-button>
        </div>
      </div>

      <!-- 主要阅读区域 -->
      <div class="reader-content" v-if="!loading && !error">
        <div v-if="pages.length > 0" class="page-container">
          <img
            :src="getCurrentPageUrl()"
            :alt="`第${currentPage + 1}页`"
            class="comic-page"
            @load="handleImageLoad"
            @error="handleImageError"
            @click.stop="nextPage"
          />
        </div>
        <div v-else style="color: white; text-align: center;">
          <p>该章节暂无内容</p>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <div style="text-align: center;">
          <i class="el-icon-loading" style="font-size: 24px;"></i>
          <p style="margin-top: 10px;">加载中...</p>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="error">
        <i class="el-icon-warning" style="font-size: 48px; margin-bottom: 20px;"></i>
        <p>{{ error }}</p>
        <el-button @click="loadChapter" type="primary" style="margin-top: 20px;">重新加载</el-button>
      </div>

      <!-- 底部控制栏 -->
      <div class="reader-controls" :class="{ hidden: !showControls }" v-if="!loading && !error && pages.length > 0">
        <div class="controls-left">
          <el-button
            @click="previousPage"
            :disabled="currentPage === 0"
            size="small"
            type="primary"
          >
            上一页
          </el-button>
          <el-button
            @click="nextPage"
            :disabled="currentPage === pages.length - 1"
            size="small"
            type="primary"
          >
            下一页
          </el-button>
        </div>

        <div class="page-info">
          {{ currentPage + 1 }} / {{ pages.length }}
        </div>

        <div class="controls-right">
          <el-button
            @click="previousChapter"
            :disabled="!hasPreviousChapter"
            size="small"
          >
            上一章
          </el-button>
          <el-button
            @click="nextChapter"
            :disabled="!hasNextChapter"
            size="small"
          >
            下一章
          </el-button>
        </div>
      </div>
    </div>
  </div>

  <!-- Vue.js -->
  <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
  <!-- ElementUI JS -->
  <script src="https://unpkg.com/element-ui/lib/index.js"></script>

  <script>
    new Vue({
      el: '#app',
      data: {
        chapter: null,
        pages: [],
        currentPage: 0,
        loading: false,
        error: null,
        showControls: true,
        controlsTimer: null,
        chapterId: null,
        hasPreviousChapter: false,
        hasNextChapter: true
      },
      computed: {
        chapterTitle() {
          return this.chapter ? this.chapter.title : '加载中...';
        }
      },
      created() {
        this.chapterId = this.getChapterIdFromUrl();
        this.loadChapter();

        // 添加键盘事件监听
        document.addEventListener('keydown', this.handleKeydown);
      },
      beforeDestroy() {
        // 移除键盘事件监听
        document.removeEventListener('keydown', this.handleKeydown);
        if (this.controlsTimer) {
          clearTimeout(this.controlsTimer);
        }
      },
      methods: {
        getChapterIdFromUrl() {
          const urlParams = new URLSearchParams(window.location.search);
          return urlParams.get('chapterId') || '1';
        },

        loadChapter() {
          this.loading = true;
          this.error = null;
          this.currentPage = 0;

          // 使用模拟数据
          setTimeout(() => {
            const mockChapter = this.getMockChapter(this.chapterId);
            if (mockChapter) {
              this.chapter = mockChapter;
              this.pages = this.getMockPages(this.chapterId);
              this.hasPreviousChapter = parseInt(this.chapterId) > 1;
              this.hasNextChapter = parseInt(this.chapterId) < 5;
            } else {
              this.error = '章节不存在';
            }
            this.loading = false;
          }, 800);
        },

        getMockChapter(id) {
          const chapters = {
            '1': { id: 1, title: "第1话：致两千年后的你" },
            '2': { id: 2, title: "第2话：那一天" },
            '3': { id: 3, title: "第3话：解散式之夜" },
            '4': { id: 4, title: "第4话：初阵" },
            '5': { id: 5, title: "第5话：在托洛斯特区攻防战" }
          };
          return chapters[id];
        },

        getMockPages(chapterId) {
          // 为每个章节生成5页模拟图片
          const pages = [];
          for (let i = 1; i <= 5; i++) {
            pages.push({
              id: (parseInt(chapterId) - 1) * 5 + i,
              image_url: `https://picsum.photos/800/1200?random=${(parseInt(chapterId) - 1) * 5 + i + 100}`,
              page_number: i
            });
          }
          return pages;
        },

        getCurrentPageUrl() {
          if (this.pages.length === 0 || this.currentPage >= this.pages.length) {
            return '';
          }
          return this.pages[this.currentPage].image_url;
        },

        nextPage() {
          if (this.currentPage < this.pages.length - 1) {
            this.currentPage++;
          } else if (this.hasNextChapter) {
            this.nextChapter();
          }
        },

        previousPage() {
          if (this.currentPage > 0) {
            this.currentPage--;
          } else if (this.hasPreviousChapter) {
            this.previousChapter();
          }
        },

        nextChapter() {
          if (this.hasNextChapter) {
            const nextId = parseInt(this.chapterId) + 1;
            window.location.href = `reader.html?chapterId=${nextId}`;
          }
        },

        previousChapter() {
          if (this.hasPreviousChapter) {
            const prevId = parseInt(this.chapterId) - 1;
            window.location.href = `reader.html?chapterId=${prevId}`;
          }
        },

        toggleControls() {
          this.showControls = !this.showControls;

          // 自动隐藏控制栏
          if (this.showControls) {
            this.autoHideControls();
          }
        },

        autoHideControls() {
          if (this.controlsTimer) {
            clearTimeout(this.controlsTimer);
          }
          this.controlsTimer = setTimeout(() => {
            this.showControls = false;
          }, 3000);
        },

        handleKeydown(event) {
          switch (event.key) {
            case 'ArrowLeft':
              this.previousPage();
              break;
            case 'ArrowRight':
              this.nextPage();
              break;
            case 'Escape':
              this.goBack();
              break;
          }
        },

        handleImageLoad() {
          console.log('图片加载完成');
        },

        handleImageError(event) {
          event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjEyMDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7lm77niYfliqDovb3lpLHotKU8L3RleHQ+PC9zdmc+';
        },

        goBack() {
          window.history.back();
        },

        goHome() {
          window.location.href = 'index.html';
        }
      }
    });
  </script>
</body>
</html>

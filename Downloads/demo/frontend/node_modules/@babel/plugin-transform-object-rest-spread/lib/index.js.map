{"version": 3, "file": "index.js", "sources": ["../src/shouldStoreRHSInTemporaryVariable.ts", "../src/compat-data.ts", "../src/index.ts"], "sourcesContent": ["import type { types as t } from \"@babel/core\";\n\n/**\n * This is a helper function to determine if we should create an intermediate variable\n * such that the RHS of an assignment is not duplicated.\n *\n * See https://github.com/babel/babel/pull/13711#issuecomment-914388382 for discussion\n * on further optimizations.\n */\nexport default function shouldStoreRHSInTemporaryVariable(\n  node: t.LVal,\n): boolean {\n  if (!node) return false;\n  if (node.type === \"ArrayPattern\") {\n    const nonNullElements = node.elements.filter(element => element !== null);\n    if (nonNullElements.length > 1) return true;\n    else return shouldStoreRHSInTemporaryVariable(nonNullElements[0]);\n  } else if (node.type === \"ObjectPattern\") {\n    const { properties } = node;\n    if (properties.length > 1) return true;\n    else if (properties.length === 0) return false;\n    else {\n      const firstProperty = properties[0];\n      if (firstProperty.type === \"ObjectProperty\") {\n        // the value of the property must be an LVal\n        return shouldStoreRHSInTemporaryVariable(firstProperty.value as t.LVal);\n      } else {\n        return shouldStoreRHSInTemporaryVariable(firstProperty);\n      }\n    }\n  } else if (node.type === \"AssignmentPattern\") {\n    return shouldStoreRHSInTemporaryVariable(node.left);\n  } else if (node.type === \"RestElement\") {\n    if (node.argument.type === \"Identifier\") return true;\n    return shouldStoreRHSInTemporaryVariable(node.argument);\n  } else {\n    // node is Identifier or MemberExpression\n    return false;\n  }\n}\n", "export default {\n  \"Object.assign\": {\n    chrome: \"49\",\n    opera: \"36\",\n    edge: \"13\",\n    firefox: \"36\",\n    safari: \"10\",\n    node: \"6\",\n    deno: \"1\",\n    ios: \"10\",\n    samsung: \"5\",\n    opera_mobile: \"36\",\n    electron: \"0.37\",\n  },\n};\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\nimport type { PluginPass, NodeP<PERSON>, Scope } from \"@babel/core\";\nimport { convertFunctionParams } from \"@babel/plugin-transform-parameters\";\nimport { isRequired } from \"@babel/helper-compilation-targets\";\nimport shouldStoreRHSInTemporaryVariable from \"./shouldStoreRHSInTemporaryVariable.ts\";\nimport compatData from \"./compat-data.ts\";\nimport { unshiftForXStatementBody } from \"@babel/plugin-transform-destructuring\";\n\n// @babel/types <=7.3.3 counts FOO as referenced in var { x: FOO }.\n// We need to detect this bug to know if \"unused\" means 0 or 1 references.\nif (!process.env.BABEL_8_BREAKING) {\n  const node = t.identifier(\"a\");\n  const property = t.objectProperty(t.identifier(\"key\"), node);\n  const pattern = t.objectPattern([property]);\n\n  // eslint-disable-next-line no-var\n  var ZERO_REFS = t.isReferenced(node, property, pattern) ? 1 : 0;\n}\n\nexport interface Options {\n  useBuiltIns?: boolean;\n  loose?: boolean;\n}\n\nexport default declare((api, opts: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const targets = api.targets();\n  const supportsObjectAssign = !isRequired(\"Object.assign\", targets, {\n    compatData,\n  });\n\n  const { useBuiltIns = supportsObjectAssign, loose = false } = opts;\n\n  if (typeof loose !== \"boolean\") {\n    throw new Error(\".loose must be a boolean, or undefined\");\n  }\n\n  const ignoreFunctionLength = api.assumption(\"ignoreFunctionLength\") ?? loose;\n  const objectRestNoSymbols = api.assumption(\"objectRestNoSymbols\") ?? loose;\n  const pureGetters = api.assumption(\"pureGetters\") ?? loose;\n  const setSpreadProperties = api.assumption(\"setSpreadProperties\") ?? loose;\n\n  function getExtendsHelper(\n    file: PluginPass,\n  ): t.MemberExpression | t.Identifier {\n    return useBuiltIns\n      ? t.memberExpression(t.identifier(\"Object\"), t.identifier(\"assign\"))\n      : file.addHelper(\"extends\");\n  }\n\n  function* iterateObjectRestElement(\n    path: NodePath<t.LVal>,\n  ): Generator<NodePath<t.RestElement>> {\n    switch (path.type) {\n      case \"ArrayPattern\":\n        for (const elementPath of path.get(\"elements\")) {\n          if (elementPath.isRestElement()) {\n            yield* iterateObjectRestElement(elementPath.get(\"argument\"));\n          } else {\n            yield* iterateObjectRestElement(elementPath);\n          }\n        }\n        break;\n      case \"ObjectPattern\":\n        for (const propertyPath of path.get(\"properties\")) {\n          if (propertyPath.isRestElement()) {\n            yield propertyPath;\n          } else {\n            yield* iterateObjectRestElement(\n              propertyPath.get(\"value\") as NodePath<t.Pattern>,\n            );\n          }\n        }\n        break;\n      case \"AssignmentPattern\":\n        yield* iterateObjectRestElement(path.get(\"left\"));\n        break;\n      default:\n        break;\n    }\n  }\n\n  function hasObjectRestElement(path: NodePath<t.LVal>): boolean {\n    const objectRestPatternIterator = iterateObjectRestElement(path);\n    return !objectRestPatternIterator.next().done;\n  }\n\n  function visitObjectRestElements(\n    path: NodePath<t.LVal>,\n    visitor: (path: NodePath<t.RestElement>) => void,\n  ) {\n    for (const restElementPath of iterateObjectRestElement(path)) {\n      visitor(restElementPath);\n    }\n  }\n\n  function hasSpread(node: t.ObjectExpression): boolean {\n    for (const prop of node.properties) {\n      if (t.isSpreadElement(prop)) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  // returns an array of all keys of an object, and a status flag indicating if all extracted keys\n  // were converted to stringLiterals or not\n  // e.g. extracts {keys: [\"a\", \"b\", \"3\", ++x], allPrimitives: false }\n  // from ast of {a: \"foo\", b, 3: \"bar\", [++x]: \"baz\"}\n  // `allPrimitives: false` doesn't necessarily mean that there is a non-primitive, but just\n  // that we are not sure.\n  function extractNormalizedKeys(node: t.ObjectPattern) {\n    // RestElement has been removed in createObjectRest\n    const props = node.properties as t.ObjectProperty[];\n    const keys: t.Expression[] = [];\n    let allPrimitives = true;\n    let hasTemplateLiteral = false;\n\n    for (const prop of props) {\n      const { key } = prop;\n      if (t.isIdentifier(key) && !prop.computed) {\n        // since a key {a: 3} is equivalent to {\"a\": 3}, use the latter\n        keys.push(t.stringLiteral(key.name));\n      } else if (t.isTemplateLiteral(key)) {\n        keys.push(t.cloneNode(key));\n        hasTemplateLiteral = true;\n      } else if (t.isLiteral(key)) {\n        keys.push(\n          t.stringLiteral(\n            String(\n              // @ts-expect-error prop.key can not be a NullLiteral\n              key.value,\n            ),\n          ),\n        );\n      } else {\n        // @ts-expect-error private name has been handled by destructuring-private\n        keys.push(t.cloneNode(key));\n\n        if (\n          (t.isMemberExpression(key, { computed: false }) &&\n            t.isIdentifier(key.object, { name: \"Symbol\" })) ||\n          (t.isCallExpression(key) &&\n            t.matchesPattern(key.callee, \"Symbol.for\"))\n        ) {\n          // there all return a primitive\n        } else {\n          allPrimitives = false;\n        }\n      }\n    }\n\n    return { keys, allPrimitives, hasTemplateLiteral };\n  }\n\n  // replaces impure computed keys with new identifiers\n  // and returns variable declarators of these new identifiers\n  function replaceImpureComputedKeys(\n    properties: NodePath<t.ObjectProperty>[],\n    scope: Scope,\n  ) {\n    const impureComputedPropertyDeclarators: t.VariableDeclarator[] = [];\n    for (const propPath of properties) {\n      // PrivateName is handled in destructuring-private plugin\n      const key = propPath.get(\"key\") as NodePath<t.Expression>;\n      if (propPath.node.computed && !key.isPure()) {\n        const name = scope.generateUidBasedOnNode(key.node);\n        const declarator = t.variableDeclarator(t.identifier(name), key.node);\n        impureComputedPropertyDeclarators.push(declarator);\n        key.replaceWith(t.identifier(name));\n      }\n    }\n    return impureComputedPropertyDeclarators;\n  }\n\n  function removeUnusedExcludedKeys(path: NodePath<t.ObjectPattern>): void {\n    const bindings = path.getOuterBindingIdentifierPaths();\n\n    Object.keys(bindings).forEach(bindingName => {\n      const bindingParentPath = bindings[bindingName].parentPath;\n      if (\n        path.scope.getBinding(bindingName).references >\n          (process.env.BABEL_8_BREAKING ? 0 : ZERO_REFS) ||\n        !bindingParentPath.isObjectProperty()\n      ) {\n        return;\n      }\n      bindingParentPath.remove();\n    });\n  }\n\n  //expects path to an object pattern\n  function createObjectRest(\n    path: NodePath<t.ObjectPattern>,\n    file: PluginPass,\n    objRef: t.Identifier | t.MemberExpression,\n  ): [\n    t.VariableDeclarator[],\n    t.AssignmentExpression[\"left\"],\n    t.CallExpression,\n  ] {\n    const props = path.get(\"properties\");\n    const last = props[props.length - 1];\n    t.assertRestElement(last.node);\n    const restElement = t.cloneNode(last.node);\n    last.remove();\n\n    const impureComputedPropertyDeclarators = replaceImpureComputedKeys(\n      path.get(\"properties\") as NodePath<t.ObjectProperty>[],\n      path.scope,\n    );\n    const { keys, allPrimitives, hasTemplateLiteral } = extractNormalizedKeys(\n      path.node,\n    );\n\n    if (keys.length === 0) {\n      return [\n        impureComputedPropertyDeclarators,\n        restElement.argument,\n        t.callExpression(getExtendsHelper(file), [\n          t.objectExpression([]),\n          t.sequenceExpression([\n            t.callExpression(file.addHelper(\"objectDestructuringEmpty\"), [\n              t.cloneNode(objRef),\n            ]),\n            t.cloneNode(objRef),\n          ]),\n        ]),\n      ];\n    }\n\n    let keyExpression;\n    if (!allPrimitives) {\n      // map to toPropertyKey to handle the possible non-string values\n      keyExpression = t.callExpression(\n        t.memberExpression(t.arrayExpression(keys), t.identifier(\"map\")),\n        [file.addHelper(\"toPropertyKey\")],\n      );\n    } else {\n      keyExpression = t.arrayExpression(keys);\n\n      if (!hasTemplateLiteral && !t.isProgram(path.scope.block)) {\n        // Hoist definition of excluded keys, so that it's not created each time.\n        const program = path.findParent(path => path.isProgram());\n        const id = path.scope.generateUidIdentifier(\"excluded\");\n\n        program.scope.push({\n          id,\n          init: keyExpression,\n          kind: \"const\",\n        });\n\n        keyExpression = t.cloneNode(id);\n      }\n    }\n\n    return [\n      impureComputedPropertyDeclarators,\n      restElement.argument,\n      t.callExpression(\n        file.addHelper(\n          `objectWithoutProperties${objectRestNoSymbols ? \"Loose\" : \"\"}`,\n        ),\n        [t.cloneNode(objRef), keyExpression],\n      ),\n    ];\n  }\n\n  function replaceRestElement(\n    parentPath: NodePath<t.Function | t.CatchClause>,\n    paramPath: NodePath<\n      t.Function[\"params\"][number] | t.AssignmentPattern[\"left\"]\n    >,\n    container?: t.VariableDeclaration[],\n  ): void {\n    if (paramPath.isAssignmentPattern()) {\n      replaceRestElement(parentPath, paramPath.get(\"left\"), container);\n      return;\n    }\n\n    if (paramPath.isArrayPattern() && hasObjectRestElement(paramPath)) {\n      const elements = paramPath.get(\"elements\");\n\n      for (let i = 0; i < elements.length; i++) {\n        replaceRestElement(parentPath, elements[i], container);\n      }\n    }\n\n    if (paramPath.isObjectPattern() && hasObjectRestElement(paramPath)) {\n      const uid = parentPath.scope.generateUidIdentifier(\"ref\");\n\n      const declar = t.variableDeclaration(\"let\", [\n        t.variableDeclarator(paramPath.node, uid),\n      ]);\n\n      if (container) {\n        container.push(declar);\n      } else {\n        parentPath.ensureBlock();\n        (parentPath.get(\"body\") as NodePath<t.BlockStatement>).unshiftContainer(\n          \"body\",\n          declar,\n        );\n      }\n      paramPath.replaceWith(t.cloneNode(uid));\n    }\n  }\n\n  return {\n    name: \"transform-object-rest-spread\",\n    manipulateOptions: process.env.BABEL_8_BREAKING\n      ? undefined\n      : (_, parser) => parser.plugins.push(\"objectRestSpread\"),\n\n    visitor: {\n      // function a({ b, ...c }) {}\n      Function(path) {\n        const params = path.get(\"params\");\n        const paramsWithRestElement = new Set<number>();\n        const idsInRestParams = new Set();\n        for (let i = 0; i < params.length; ++i) {\n          const param = params[i];\n          if (hasObjectRestElement(param)) {\n            paramsWithRestElement.add(i);\n            for (const name of Object.keys(param.getBindingIdentifiers())) {\n              idsInRestParams.add(name);\n            }\n          }\n        }\n\n        // if true, a parameter exists that has an id in its initializer\n        // that is also an id bound in a rest parameter\n        // example: f({...R}, a = R)\n        let idInRest = false;\n\n        const IdentifierHandler = function (\n          path: NodePath<t.Identifier>,\n          functionScope: Scope,\n        ) {\n          const name = path.node.name;\n          if (\n            path.scope.getBinding(name) === functionScope.getBinding(name) &&\n            idsInRestParams.has(name)\n          ) {\n            idInRest = true;\n            path.stop();\n          }\n        };\n\n        let i: number;\n        for (i = 0; i < params.length && !idInRest; ++i) {\n          const param = params[i];\n          if (!paramsWithRestElement.has(i)) {\n            if (param.isReferencedIdentifier() || param.isBindingIdentifier()) {\n              IdentifierHandler(param, path.scope);\n            } else {\n              param.traverse(\n                {\n                  \"Scope|TypeAnnotation|TSTypeAnnotation\": path => path.skip(),\n                  \"ReferencedIdentifier|BindingIdentifier\": IdentifierHandler,\n                },\n                path.scope,\n              );\n            }\n          }\n        }\n\n        if (!idInRest) {\n          for (let i = 0; i < params.length; ++i) {\n            const param = params[i];\n            if (paramsWithRestElement.has(i)) {\n              replaceRestElement(path, param);\n            }\n          }\n        } else {\n          const shouldTransformParam = (idx: number) =>\n            idx >= i - 1 || paramsWithRestElement.has(idx);\n          convertFunctionParams(\n            path,\n            ignoreFunctionLength,\n            shouldTransformParam,\n            replaceRestElement,\n          );\n        }\n      },\n\n      // adapted from transform-destructuring/src/index.js#pushObjectRest\n      // const { a, ...b } = c;\n      VariableDeclarator(path, file) {\n        if (!path.get(\"id\").isObjectPattern()) {\n          return;\n        }\n\n        let insertionPath = path;\n        const originalPath = path;\n\n        visitObjectRestElements(path.get(\"id\"), path => {\n          if (\n            // skip single-property case, e.g.\n            // const { ...x } = foo();\n            // since the RHS will not be duplicated\n            shouldStoreRHSInTemporaryVariable(originalPath.node.id) &&\n            !t.isIdentifier(originalPath.node.init)\n          ) {\n            // const { a, ...b } = foo();\n            // to avoid calling foo() twice, as a first step convert it to:\n            // const _foo = foo(),\n            //       { a, ...b } = _foo;\n            const initRef = path.scope.generateUidIdentifierBasedOnNode(\n              originalPath.node.init,\n              \"ref\",\n            );\n            // insert _foo = foo()\n            originalPath.insertBefore(\n              t.variableDeclarator(initRef, originalPath.node.init),\n            );\n            // replace foo() with _foo\n            originalPath.replaceWith(\n              t.variableDeclarator(originalPath.node.id, t.cloneNode(initRef)),\n            );\n\n            return;\n          }\n\n          let ref = originalPath.node.init;\n          const refPropertyPath: NodePath<t.ObjectProperty>[] = [];\n          let kind;\n\n          path.findParent((path: NodePath): boolean => {\n            if (path.isObjectProperty()) {\n              refPropertyPath.unshift(path);\n            } else if (path.isVariableDeclarator()) {\n              kind = path.parentPath.node.kind;\n              return true;\n            }\n          });\n\n          const impureObjRefComputedDeclarators = replaceImpureComputedKeys(\n            refPropertyPath,\n            path.scope,\n          );\n          refPropertyPath.forEach(prop => {\n            const { node } = prop;\n            ref = t.memberExpression(\n              ref,\n              t.cloneNode(node.key),\n              node.computed || t.isLiteral(node.key),\n            );\n          });\n\n          const objectPatternPath =\n            path.parentPath as NodePath<t.ObjectPattern>;\n\n          const [impureComputedPropertyDeclarators, argument, callExpression] =\n            createObjectRest(\n              objectPatternPath,\n              file,\n              ref as t.MemberExpression,\n            );\n\n          if (pureGetters) {\n            removeUnusedExcludedKeys(objectPatternPath);\n          }\n\n          t.assertIdentifier(argument);\n\n          insertionPath.insertBefore(impureComputedPropertyDeclarators);\n\n          insertionPath.insertBefore(impureObjRefComputedDeclarators);\n\n          insertionPath = insertionPath.insertAfter(\n            t.variableDeclarator(argument, callExpression),\n          )[0] as NodePath<t.VariableDeclarator>;\n\n          path.scope.registerBinding(kind, insertionPath);\n\n          if (objectPatternPath.node.properties.length === 0) {\n            objectPatternPath\n              .findParent(\n                path => path.isObjectProperty() || path.isVariableDeclarator(),\n              )\n              .remove();\n          }\n        });\n      },\n\n      // taken from transform-destructuring/src/index.js#visitor\n      // export var { a, ...b } = c;\n      ExportNamedDeclaration(path) {\n        const declaration = path.get(\"declaration\");\n        if (!declaration.isVariableDeclaration()) return;\n\n        const hasRest = declaration\n          .get(\"declarations\")\n          .some(path => hasObjectRestElement(path.get(\"id\")));\n        if (!hasRest) return;\n\n        const specifiers = [];\n\n        for (const name of Object.keys(path.getOuterBindingIdentifiers(true))) {\n          specifiers.push(\n            t.exportSpecifier(t.identifier(name), t.identifier(name)),\n          );\n        }\n\n        // Split the declaration and export list into two declarations so that the variable\n        // declaration can be split up later without needing to worry about not being a\n        // top-level statement.\n        path.replaceWith(declaration.node);\n        path.insertAfter(t.exportNamedDeclaration(null, specifiers));\n      },\n\n      // try {} catch ({a, ...b}) {}\n      CatchClause(path) {\n        const paramPath = path.get(\"param\");\n        replaceRestElement(path, paramPath);\n      },\n\n      // ({a, ...b} = c);\n      AssignmentExpression(path, file) {\n        const leftPath = path.get(\"left\");\n        if (leftPath.isObjectPattern() && hasObjectRestElement(leftPath)) {\n          const nodes = [];\n\n          const refName = path.scope.generateUidBasedOnNode(\n            path.node.right,\n            \"ref\",\n          );\n\n          nodes.push(\n            t.variableDeclaration(\"var\", [\n              t.variableDeclarator(t.identifier(refName), path.node.right),\n            ]),\n          );\n\n          const [impureComputedPropertyDeclarators, argument, callExpression] =\n            createObjectRest(leftPath, file, t.identifier(refName));\n\n          if (impureComputedPropertyDeclarators.length > 0) {\n            nodes.push(\n              t.variableDeclaration(\"var\", impureComputedPropertyDeclarators),\n            );\n          }\n\n          const nodeWithoutSpread = t.cloneNode(path.node);\n          nodeWithoutSpread.right = t.identifier(refName);\n          nodes.push(t.expressionStatement(nodeWithoutSpread));\n          nodes.push(\n            t.expressionStatement(\n              t.assignmentExpression(\"=\", argument, callExpression),\n            ),\n          );\n          nodes.push(t.expressionStatement(t.identifier(refName)));\n\n          path.replaceWithMultiple(nodes);\n        }\n      },\n\n      // taken from transform-destructuring/src/index.js#visitor\n      ForXStatement(path: NodePath<t.ForXStatement>) {\n        const { node, scope } = path;\n        const leftPath = path.get(\"left\");\n\n        if (!leftPath.isVariableDeclaration()) {\n          if (!hasObjectRestElement(leftPath)) {\n            return;\n          }\n          // for ({a, ...b} of []) {}\n          const temp = scope.generateUidIdentifier(\"ref\");\n\n          node.left = t.variableDeclaration(\"var\", [\n            t.variableDeclarator(temp),\n          ]);\n\n          path.ensureBlock();\n\n          const statementBody = (path.node.body as t.BlockStatement).body;\n          const nodes = [];\n          // todo: the completion of a for statement can only be observed from\n          // a do block (or eval that we don't support),\n          // but the new do-expression proposal plans to ban iteration ends in the\n          // do block, maybe we can get rid of this\n          if (statementBody.length === 0 && path.isCompletionRecord()) {\n            nodes.unshift(t.expressionStatement(scope.buildUndefinedNode()));\n          }\n\n          nodes.unshift(\n            t.expressionStatement(\n              t.assignmentExpression(\"=\", leftPath.node, t.cloneNode(temp)),\n            ),\n          );\n\n          unshiftForXStatementBody(path, nodes);\n          scope.crawl();\n          return;\n        } else {\n          // for (var {a, ...b} of []) {}\n          const patternPath = leftPath.get(\"declarations\")[0].get(\"id\");\n          if (!hasObjectRestElement(patternPath)) {\n            return;\n          }\n          const left = leftPath.node;\n          const pattern = patternPath.node;\n\n          const key = scope.generateUidIdentifier(\"ref\");\n          node.left = t.variableDeclaration(left.kind, [\n            t.variableDeclarator(key, null),\n          ]);\n\n          path.ensureBlock();\n\n          unshiftForXStatementBody(path, [\n            t.variableDeclaration(node.left.kind, [\n              t.variableDeclarator(pattern, t.cloneNode(key)),\n            ]),\n          ]);\n          scope.crawl();\n          return;\n        }\n      },\n\n      // [{a, ...b}] = c;\n      ArrayPattern(path) {\n        type LhsAndRhs = { left: t.ObjectPattern; right: t.Identifier };\n        const objectPatterns: LhsAndRhs[] = [];\n        const { scope } = path;\n        const uidIdentifiers: t.Identifier[] = [];\n\n        visitObjectRestElements(path, path => {\n          const objectPattern = path.parentPath as NodePath<t.ObjectPattern>;\n\n          const uid = scope.generateUidIdentifier(\"ref\");\n          objectPatterns.push({ left: objectPattern.node, right: uid });\n          uidIdentifiers.push(uid);\n\n          objectPattern.replaceWith(t.cloneNode(uid));\n          path.skip();\n        });\n\n        if (objectPatterns.length > 0) {\n          const patternParentPath = path.findParent(\n            path => !(path.isPattern() || path.isObjectProperty()),\n          );\n          const patternParent = patternParentPath.node;\n          switch (patternParent.type) {\n            case \"VariableDeclarator\":\n              patternParentPath.insertAfter(\n                objectPatterns.map(({ left, right }) =>\n                  t.variableDeclarator(left, right),\n                ),\n              );\n              break;\n            case \"AssignmentExpression\":\n              {\n                for (const uidIdentifier of uidIdentifiers) {\n                  scope.push({ id: t.cloneNode(uidIdentifier) });\n                }\n                patternParentPath.insertAfter(\n                  objectPatterns.map(({ left, right }) =>\n                    t.assignmentExpression(\"=\", left, right),\n                  ),\n                );\n              }\n              break;\n            default:\n              throw new Error(\n                `Unexpected pattern parent type: ${patternParent.type}`,\n              );\n          }\n        }\n      },\n\n      // var a = { ...b, ...c }\n      ObjectExpression(path, file) {\n        if (!hasSpread(path.node)) return;\n\n        let helper: t.Identifier | t.MemberExpression;\n        if (setSpreadProperties) {\n          helper = getExtendsHelper(file);\n        } else {\n          if (process.env.BABEL_8_BREAKING) {\n            helper = file.addHelper(\"objectSpread2\");\n          } else {\n            try {\n              helper = file.addHelper(\"objectSpread2\");\n            } catch {\n              // TODO: This is needed to workaround https://github.com/babel/babel/issues/10187\n              // and https://github.com/babel/babel/issues/10179 for older @babel/core versions\n              // where #10187 isn't fixed.\n              this.file.declarations.objectSpread2 = null;\n\n              // objectSpread2 has been introduced in v7.5.0\n              // We have to maintain backward compatibility.\n              helper = file.addHelper(\"objectSpread\");\n            }\n          }\n        }\n\n        let exp: t.CallExpression = null;\n        let props: t.ObjectMember[] = [];\n\n        function make() {\n          const hadProps = props.length > 0;\n          const obj = t.objectExpression(props);\n          props = [];\n\n          if (!exp) {\n            exp = t.callExpression(helper, [obj]);\n            return;\n          }\n\n          // When we can assume that getters are pure and don't depend on\n          // the order of evaluation, we can avoid making multiple calls.\n          if (pureGetters) {\n            if (hadProps) {\n              exp.arguments.push(obj);\n            }\n            return;\n          }\n\n          exp = t.callExpression(t.cloneNode(helper), [\n            exp,\n            // If we have static props, we need to insert an empty object\n            // because the odd arguments are copied with [[Get]], not\n            // [[GetOwnProperty]]\n            ...(hadProps ? [t.objectExpression([]), obj] : []),\n          ]);\n        }\n\n        for (const prop of path.node.properties) {\n          if (t.isSpreadElement(prop)) {\n            make();\n            exp.arguments.push(prop.argument);\n          } else {\n            props.push(prop);\n          }\n        }\n\n        if (props.length) make();\n\n        path.replaceWith(exp);\n      },\n    },\n  };\n});\n"], "names": ["shouldStoreRHSInTemporaryVariable", "node", "type", "nonNullElements", "elements", "filter", "element", "length", "properties", "firstProperty", "value", "left", "argument", "chrome", "opera", "edge", "firefox", "safari", "deno", "ios", "samsung", "opera_mobile", "electron", "t", "identifier", "property", "objectProperty", "pattern", "objectPattern", "ZERO_REFS", "isReferenced", "declare", "api", "opts", "_api$assumption", "_api$assumption2", "_api$assumption3", "_api$assumption4", "assertVersion", "targets", "supportsObjectAssign", "isRequired", "compatData", "useBuiltIns", "loose", "Error", "ignoreFunctionLength", "assumption", "objectRestNoSymbols", "pureGetters", "setSpreadProperties", "getExtendsHelper", "file", "memberExpression", "addHelper", "iterateObjectRestElement", "path", "elementPath", "get", "isRestElement", "propertyPath", "hasObjectRestElement", "objectRestPatternIterator", "next", "done", "visitObjectRestElements", "visitor", "restElement<PERSON>ath", "hasSpread", "prop", "isSpreadElement", "extractNormalizedKeys", "props", "keys", "allPrimitives", "hasTemplateLiteral", "key", "isIdentifier", "computed", "push", "stringLiteral", "name", "isTemplateLiteral", "cloneNode", "isLiteral", "String", "isMemberExpression", "object", "isCallExpression", "matchesPattern", "callee", "replaceImpureComputedKeys", "scope", "impureComputedPropertyDeclarators", "prop<PERSON>ath", "isPure", "generateUidBasedOnNode", "declarator", "variableDeclarator", "replaceWith", "removeUnusedExcludedKeys", "bindings", "getOuterBindingIdentifierPaths", "Object", "for<PERSON>ach", "bindingName", "bindingParentPath", "parentPath", "getBinding", "references", "isObjectProperty", "remove", "createObjectRest", "objRef", "last", "assertRestElement", "restElement", "callExpression", "objectExpression", "sequenceExpression", "keyExpression", "arrayExpression", "isProgram", "block", "program", "findParent", "id", "generateUidIdentifier", "init", "kind", "replaceRestElement", "<PERSON><PERSON><PERSON><PERSON>", "container", "isAssignmentPattern", "isArrayPattern", "i", "isObjectPattern", "uid", "declar", "variableDeclaration", "ensureBlock", "unshiftContainer", "manipulateOptions", "_", "parser", "plugins", "Function", "params", "paramsWithRestElement", "Set", "idsInRestParams", "param", "add", "getBindingIdentifiers", "idInRest", "IdentifierHandler", "functionScope", "has", "stop", "isReferencedIdentifier", "isBindingIdentifier", "traverse", "skip", "shouldTransformParam", "idx", "convertFunctionParams", "VariableDeclarator", "insertionPath", "originalPath", "initRef", "generateUidIdentifierBasedOnNode", "insertBefore", "ref", "refProper<PERSON><PERSON>ath", "unshift", "isVariableDeclarator", "impureObjRefComputedDeclarators", "objectPatternPath", "assertIdentifier", "insertAfter", "registerBinding", "ExportNamedDeclaration", "declaration", "isVariableDeclaration", "hasRest", "some", "specifiers", "getOuterBindingIdentifiers", "exportSpecifier", "exportNamedDeclaration", "CatchClause", "AssignmentExpression", "leftPath", "nodes", "refName", "right", "nodeWithoutSpread", "expressionStatement", "assignmentExpression", "replaceWithMultiple", "ForXStatement", "temp", "statementBody", "body", "isCompletionRecord", "buildUndefinedNode", "unshiftForXStatementBody", "crawl", "patternPath", "ArrayPattern", "objectPatterns", "uidIdentifiers", "patternParentPath", "isPattern", "patternParent", "map", "uidIdentifier", "ObjectExpression", "helper", "_unused", "declarations", "objectSpread2", "exp", "make", "hadProps", "obj", "arguments"], "mappings": ";;;;;;;;;;AASe,SAASA,iCAAiCA,CACvDC,IAAY,EACH;AACT,EAAA,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK,CAAA;AACvB,EAAA,IAAIA,IAAI,CAACC,IAAI,KAAK,cAAc,EAAE;AAChC,IAAA,MAAMC,eAAe,GAAGF,IAAI,CAACG,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,KAAK,IAAI,CAAC,CAAA;AACzE,IAAA,IAAIH,eAAe,CAACI,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,KACvC,OAAOP,iCAAiC,CAACG,eAAe,CAAC,CAAC,CAAC,CAAC,CAAA;AACnE,GAAC,MAAM,IAAIF,IAAI,CAACC,IAAI,KAAK,eAAe,EAAE;IACxC,MAAM;AAAEM,MAAAA,UAAAA;AAAW,KAAC,GAAGP,IAAI,CAAA;IAC3B,IAAIO,UAAU,CAACD,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,KAClC,IAAIC,UAAU,CAACD,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC,KAC1C;AACH,MAAA,MAAME,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC,CAAA;AACnC,MAAA,IAAIC,aAAa,CAACP,IAAI,KAAK,gBAAgB,EAAE;AAE3C,QAAA,OAAOF,iCAAiC,CAACS,aAAa,CAACC,KAAe,CAAC,CAAA;AACzE,OAAC,MAAM;QACL,OAAOV,iCAAiC,CAACS,aAAa,CAAC,CAAA;AACzD,OAAA;AACF,KAAA;AACF,GAAC,MAAM,IAAIR,IAAI,CAACC,IAAI,KAAK,mBAAmB,EAAE;AAC5C,IAAA,OAAOF,iCAAiC,CAACC,IAAI,CAACU,IAAI,CAAC,CAAA;AACrD,GAAC,MAAM,IAAIV,IAAI,CAACC,IAAI,KAAK,aAAa,EAAE;IACtC,IAAID,IAAI,CAACW,QAAQ,CAACV,IAAI,KAAK,YAAY,EAAE,OAAO,IAAI,CAAA;AACpD,IAAA,OAAOF,iCAAiC,CAACC,IAAI,CAACW,QAAQ,CAAC,CAAA;AACzD,GAAC,MAAM;AAEL,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AACF;;ACvCA,iBAAe;AACb,EAAA,eAAe,EAAE;AACfC,IAAAA,MAAM,EAAE,IAAI;AACZC,IAAAA,KAAK,EAAE,IAAI;AACXC,IAAAA,IAAI,EAAE,IAAI;AACVC,IAAAA,OAAO,EAAE,IAAI;AACbC,IAAAA,MAAM,EAAE,IAAI;AACZhB,IAAAA,IAAI,EAAE,GAAG;AACTiB,IAAAA,IAAI,EAAE,GAAG;AACTC,IAAAA,GAAG,EAAE,IAAI;AACTC,IAAAA,OAAO,EAAE,GAAG;AACZC,IAAAA,YAAY,EAAE,IAAI;AAClBC,IAAAA,QAAQ,EAAE,MAAA;AACZ,GAAA;AACF,CAAC;;ACHkC;AACjC,EAAA,MAAMrB,IAAI,GAAGsB,UAAC,CAACC,UAAU,CAAC,GAAG,CAAC,CAAA;AAC9B,EAAA,MAAMC,QAAQ,GAAGF,UAAC,CAACG,cAAc,CAACH,UAAC,CAACC,UAAU,CAAC,KAAK,CAAC,EAAEvB,IAAI,CAAC,CAAA;EAC5D,MAAM0B,OAAO,GAAGJ,UAAC,CAACK,aAAa,CAAC,CAACH,QAAQ,CAAC,CAAC,CAAA;AAG3C,EAAA,IAAII,SAAS,GAAGN,UAAC,CAACO,YAAY,CAAC7B,IAAI,EAAEwB,QAAQ,EAAEE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;AACjE,CAAA;AAOA,YAAeI,yBAAO,CAAC,CAACC,GAAG,EAAEC,IAAa,KAAK;AAAA,EAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,CAAA;EAC7CL,GAAG,CAACM,aAAa,CAAA,sCAAoB,CAAC,CAAA;AAEtC,EAAA,MAAMC,OAAO,GAAGP,GAAG,CAACO,OAAO,EAAE,CAAA;EAC7B,MAAMC,oBAAoB,GAAG,CAACC,mCAAU,CAAC,eAAe,EAAEF,OAAO,EAAE;AACjEG,IAAAA,UAAAA;AACF,GAAC,CAAC,CAAA;EAEF,MAAM;AAAEC,IAAAA,WAAW,GAAGH,oBAAoB;AAAEI,IAAAA,KAAK,GAAG,KAAA;AAAM,GAAC,GAAGX,IAAI,CAAA;AAElE,EAAA,IAAI,OAAOW,KAAK,KAAK,SAAS,EAAE;AAC9B,IAAA,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC,CAAA;AAC3D,GAAA;AAEA,EAAA,MAAMC,oBAAoB,GAAA,CAAAZ,eAAA,GAAGF,GAAG,CAACe,UAAU,CAAC,sBAAsB,CAAC,KAAAb,IAAAA,GAAAA,eAAA,GAAIU,KAAK,CAAA;AAC5E,EAAA,MAAMI,mBAAmB,GAAA,CAAAb,gBAAA,GAAGH,GAAG,CAACe,UAAU,CAAC,qBAAqB,CAAC,KAAAZ,IAAAA,GAAAA,gBAAA,GAAIS,KAAK,CAAA;AAC1E,EAAA,MAAMK,WAAW,GAAA,CAAAb,gBAAA,GAAGJ,GAAG,CAACe,UAAU,CAAC,aAAa,CAAC,KAAAX,IAAAA,GAAAA,gBAAA,GAAIQ,KAAK,CAAA;AAC1D,EAAA,MAAMM,mBAAmB,GAAA,CAAAb,gBAAA,GAAGL,GAAG,CAACe,UAAU,CAAC,qBAAqB,CAAC,KAAAV,IAAAA,GAAAA,gBAAA,GAAIO,KAAK,CAAA;EAE1E,SAASO,gBAAgBA,CACvBC,IAAgB,EACmB;IACnC,OAAOT,WAAW,GACdpB,UAAC,CAAC8B,gBAAgB,CAAC9B,UAAC,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAED,UAAC,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAC,GAClE4B,IAAI,CAACE,SAAS,CAAC,SAAS,CAAC,CAAA;AAC/B,GAAA;EAEA,UAAUC,wBAAwBA,CAChCC,IAAsB,EACc;IACpC,QAAQA,IAAI,CAACtD,IAAI;AACf,MAAA,KAAK,cAAc;QACjB,KAAK,MAAMuD,WAAW,IAAID,IAAI,CAACE,GAAG,CAAC,UAAU,CAAC,EAAE;AAC9C,UAAA,IAAID,WAAW,CAACE,aAAa,EAAE,EAAE;YAC/B,OAAOJ,wBAAwB,CAACE,WAAW,CAACC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAA;AAC9D,WAAC,MAAM;YACL,OAAOH,wBAAwB,CAACE,WAAW,CAAC,CAAA;AAC9C,WAAA;AACF,SAAA;AACA,QAAA,MAAA;AACF,MAAA,KAAK,eAAe;QAClB,KAAK,MAAMG,YAAY,IAAIJ,IAAI,CAACE,GAAG,CAAC,YAAY,CAAC,EAAE;AACjD,UAAA,IAAIE,YAAY,CAACD,aAAa,EAAE,EAAE;AAChC,YAAA,MAAMC,YAAY,CAAA;AACpB,WAAC,MAAM;YACL,OAAOL,wBAAwB,CAC7BK,YAAY,CAACF,GAAG,CAAC,OAAO,CAC1B,CAAC,CAAA;AACH,WAAA;AACF,SAAA;AACA,QAAA,MAAA;AACF,MAAA,KAAK,mBAAmB;QACtB,OAAOH,wBAAwB,CAACC,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAA;AACjD,QAAA,MAAA;AAGJ,KAAA;AACF,GAAA;EAEA,SAASG,oBAAoBA,CAACL,IAAsB,EAAW;AAC7D,IAAA,MAAMM,yBAAyB,GAAGP,wBAAwB,CAACC,IAAI,CAAC,CAAA;AAChE,IAAA,OAAO,CAACM,yBAAyB,CAACC,IAAI,EAAE,CAACC,IAAI,CAAA;AAC/C,GAAA;AAEA,EAAA,SAASC,uBAAuBA,CAC9BT,IAAsB,EACtBU,OAAgD,EAChD;AACA,IAAA,KAAK,MAAMC,eAAe,IAAIZ,wBAAwB,CAACC,IAAI,CAAC,EAAE;MAC5DU,OAAO,CAACC,eAAe,CAAC,CAAA;AAC1B,KAAA;AACF,GAAA;EAEA,SAASC,SAASA,CAACnE,IAAwB,EAAW;AACpD,IAAA,KAAK,MAAMoE,IAAI,IAAIpE,IAAI,CAACO,UAAU,EAAE;AAClC,MAAA,IAAIe,UAAC,CAAC+C,eAAe,CAACD,IAAI,CAAC,EAAE;AAC3B,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;AACF,KAAA;AACA,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;EAQA,SAASE,qBAAqBA,CAACtE,IAAqB,EAAE;AAEpD,IAAA,MAAMuE,KAAK,GAAGvE,IAAI,CAACO,UAAgC,CAAA;IACnD,MAAMiE,IAAoB,GAAG,EAAE,CAAA;IAC/B,IAAIC,aAAa,GAAG,IAAI,CAAA;IACxB,IAAIC,kBAAkB,GAAG,KAAK,CAAA;AAE9B,IAAA,KAAK,MAAMN,IAAI,IAAIG,KAAK,EAAE;MACxB,MAAM;AAAEI,QAAAA,GAAAA;AAAI,OAAC,GAAGP,IAAI,CAAA;MACpB,IAAI9C,UAAC,CAACsD,YAAY,CAACD,GAAG,CAAC,IAAI,CAACP,IAAI,CAACS,QAAQ,EAAE;QAEzCL,IAAI,CAACM,IAAI,CAACxD,UAAC,CAACyD,aAAa,CAACJ,GAAG,CAACK,IAAI,CAAC,CAAC,CAAA;OACrC,MAAM,IAAI1D,UAAC,CAAC2D,iBAAiB,CAACN,GAAG,CAAC,EAAE;QACnCH,IAAI,CAACM,IAAI,CAACxD,UAAC,CAAC4D,SAAS,CAACP,GAAG,CAAC,CAAC,CAAA;AAC3BD,QAAAA,kBAAkB,GAAG,IAAI,CAAA;OAC1B,MAAM,IAAIpD,UAAC,CAAC6D,SAAS,CAACR,GAAG,CAAC,EAAE;AAC3BH,QAAAA,IAAI,CAACM,IAAI,CACPxD,UAAC,CAACyD,aAAa,CACbK,MAAM,CAEJT,GAAG,CAAClE,KACN,CACF,CACF,CAAC,CAAA;AACH,OAAC,MAAM;QAEL+D,IAAI,CAACM,IAAI,CAACxD,UAAC,CAAC4D,SAAS,CAACP,GAAG,CAAC,CAAC,CAAA;AAE3B,QAAA,IACGrD,UAAC,CAAC+D,kBAAkB,CAACV,GAAG,EAAE;AAAEE,UAAAA,QAAQ,EAAE,KAAA;SAAO,CAAC,IAC7CvD,UAAC,CAACsD,YAAY,CAACD,GAAG,CAACW,MAAM,EAAE;AAAEN,UAAAA,IAAI,EAAE,QAAA;SAAU,CAAC,IAC/C1D,UAAC,CAACiE,gBAAgB,CAACZ,GAAG,CAAC,IACtBrD,UAAC,CAACkE,cAAc,CAACb,GAAG,CAACc,MAAM,EAAE,YAAY,CAAE,EAC7C,CAED,MAAM;AACLhB,UAAAA,aAAa,GAAG,KAAK,CAAA;AACvB,SAAA;AACF,OAAA;AACF,KAAA;IAEA,OAAO;MAAED,IAAI;MAAEC,aAAa;AAAEC,MAAAA,kBAAAA;KAAoB,CAAA;AACpD,GAAA;AAIA,EAAA,SAASgB,yBAAyBA,CAChCnF,UAAwC,EACxCoF,KAAY,EACZ;IACA,MAAMC,iCAAyD,GAAG,EAAE,CAAA;AACpE,IAAA,KAAK,MAAMC,QAAQ,IAAItF,UAAU,EAAE;AAEjC,MAAA,MAAMoE,GAAG,GAAGkB,QAAQ,CAACpC,GAAG,CAAC,KAAK,CAA2B,CAAA;AACzD,MAAA,IAAIoC,QAAQ,CAAC7F,IAAI,CAAC6E,QAAQ,IAAI,CAACF,GAAG,CAACmB,MAAM,EAAE,EAAE;QAC3C,MAAMd,IAAI,GAAGW,KAAK,CAACI,sBAAsB,CAACpB,GAAG,CAAC3E,IAAI,CAAC,CAAA;AACnD,QAAA,MAAMgG,UAAU,GAAG1E,UAAC,CAAC2E,kBAAkB,CAAC3E,UAAC,CAACC,UAAU,CAACyD,IAAI,CAAC,EAAEL,GAAG,CAAC3E,IAAI,CAAC,CAAA;AACrE4F,QAAAA,iCAAiC,CAACd,IAAI,CAACkB,UAAU,CAAC,CAAA;QAClDrB,GAAG,CAACuB,WAAW,CAAC5E,UAAC,CAACC,UAAU,CAACyD,IAAI,CAAC,CAAC,CAAA;AACrC,OAAA;AACF,KAAA;AACA,IAAA,OAAOY,iCAAiC,CAAA;AAC1C,GAAA;EAEA,SAASO,wBAAwBA,CAAC5C,IAA+B,EAAQ;AACvE,IAAA,MAAM6C,QAAQ,GAAG7C,IAAI,CAAC8C,8BAA8B,EAAE,CAAA;IAEtDC,MAAM,CAAC9B,IAAI,CAAC4B,QAAQ,CAAC,CAACG,OAAO,CAACC,WAAW,IAAI;AAC3C,MAAA,MAAMC,iBAAiB,GAAGL,QAAQ,CAACI,WAAW,CAAC,CAACE,UAAU,CAAA;AAC1D,MAAA,IACEnD,IAAI,CAACoC,KAAK,CAACgB,UAAU,CAACH,WAAW,CAAC,CAACI,UAAU,GACPhF,SAAU,IAChD,CAAC6E,iBAAiB,CAACI,gBAAgB,EAAE,EACrC;AACA,QAAA,OAAA;AACF,OAAA;MACAJ,iBAAiB,CAACK,MAAM,EAAE,CAAA;AAC5B,KAAC,CAAC,CAAA;AACJ,GAAA;AAGA,EAAA,SAASC,gBAAgBA,CACvBxD,IAA+B,EAC/BJ,IAAgB,EAChB6D,MAAyC,EAKzC;AACA,IAAA,MAAMzC,KAAK,GAAGhB,IAAI,CAACE,GAAG,CAAC,YAAY,CAAC,CAAA;IACpC,MAAMwD,IAAI,GAAG1C,KAAK,CAACA,KAAK,CAACjE,MAAM,GAAG,CAAC,CAAC,CAAA;AACpCgB,IAAAA,UAAC,CAAC4F,iBAAiB,CAACD,IAAI,CAACjH,IAAI,CAAC,CAAA;IAC9B,MAAMmH,WAAW,GAAG7F,UAAC,CAAC4D,SAAS,CAAC+B,IAAI,CAACjH,IAAI,CAAC,CAAA;IAC1CiH,IAAI,CAACH,MAAM,EAAE,CAAA;AAEb,IAAA,MAAMlB,iCAAiC,GAAGF,yBAAyB,CACjEnC,IAAI,CAACE,GAAG,CAAC,YAAY,CAAC,EACtBF,IAAI,CAACoC,KACP,CAAC,CAAA;IACD,MAAM;MAAEnB,IAAI;MAAEC,aAAa;AAAEC,MAAAA,kBAAAA;AAAmB,KAAC,GAAGJ,qBAAqB,CACvEf,IAAI,CAACvD,IACP,CAAC,CAAA;AAED,IAAA,IAAIwE,IAAI,CAAClE,MAAM,KAAK,CAAC,EAAE;AACrB,MAAA,OAAO,CACLsF,iCAAiC,EACjCuB,WAAW,CAACxG,QAAQ,EACpBW,UAAC,CAAC8F,cAAc,CAAClE,gBAAgB,CAACC,IAAI,CAAC,EAAE,CACvC7B,UAAC,CAAC+F,gBAAgB,CAAC,EAAE,CAAC,EACtB/F,UAAC,CAACgG,kBAAkB,CAAC,CACnBhG,UAAC,CAAC8F,cAAc,CAACjE,IAAI,CAACE,SAAS,CAAC,0BAA0B,CAAC,EAAE,CAC3D/B,UAAC,CAAC4D,SAAS,CAAC8B,MAAM,CAAC,CACpB,CAAC,EACF1F,UAAC,CAAC4D,SAAS,CAAC8B,MAAM,CAAC,CACpB,CAAC,CACH,CAAC,CACH,CAAA;AACH,KAAA;AAEA,IAAA,IAAIO,aAAa,CAAA;IACjB,IAAI,CAAC9C,aAAa,EAAE;AAElB8C,MAAAA,aAAa,GAAGjG,UAAC,CAAC8F,cAAc,CAC9B9F,UAAC,CAAC8B,gBAAgB,CAAC9B,UAAC,CAACkG,eAAe,CAAChD,IAAI,CAAC,EAAElD,UAAC,CAACC,UAAU,CAAC,KAAK,CAAC,CAAC,EAChE,CAAC4B,IAAI,CAACE,SAAS,CAAC,eAAe,CAAC,CAClC,CAAC,CAAA;AACH,KAAC,MAAM;AACLkE,MAAAA,aAAa,GAAGjG,UAAC,CAACkG,eAAe,CAAChD,IAAI,CAAC,CAAA;AAEvC,MAAA,IAAI,CAACE,kBAAkB,IAAI,CAACpD,UAAC,CAACmG,SAAS,CAAClE,IAAI,CAACoC,KAAK,CAAC+B,KAAK,CAAC,EAAE;AAEzD,QAAA,MAAMC,OAAO,GAAGpE,IAAI,CAACqE,UAAU,CAACrE,IAAI,IAAIA,IAAI,CAACkE,SAAS,EAAE,CAAC,CAAA;QACzD,MAAMI,EAAE,GAAGtE,IAAI,CAACoC,KAAK,CAACmC,qBAAqB,CAAC,UAAU,CAAC,CAAA;AAEvDH,QAAAA,OAAO,CAAChC,KAAK,CAACb,IAAI,CAAC;UACjB+C,EAAE;AACFE,UAAAA,IAAI,EAAER,aAAa;AACnBS,UAAAA,IAAI,EAAE,OAAA;AACR,SAAC,CAAC,CAAA;AAEFT,QAAAA,aAAa,GAAGjG,UAAC,CAAC4D,SAAS,CAAC2C,EAAE,CAAC,CAAA;AACjC,OAAA;AACF,KAAA;AAEA,IAAA,OAAO,CACLjC,iCAAiC,EACjCuB,WAAW,CAACxG,QAAQ,EACpBW,UAAC,CAAC8F,cAAc,CACdjE,IAAI,CAACE,SAAS,CACZ,CAA0BN,uBAAAA,EAAAA,mBAAmB,GAAG,OAAO,GAAG,EAAE,CAAA,CAC9D,CAAC,EACD,CAACzB,UAAC,CAAC4D,SAAS,CAAC8B,MAAM,CAAC,EAAEO,aAAa,CACrC,CAAC,CACF,CAAA;AACH,GAAA;AAEA,EAAA,SAASU,kBAAkBA,CACzBvB,UAAgD,EAChDwB,SAEC,EACDC,SAAmC,EAC7B;AACN,IAAA,IAAID,SAAS,CAACE,mBAAmB,EAAE,EAAE;MACnCH,kBAAkB,CAACvB,UAAU,EAAEwB,SAAS,CAACzE,GAAG,CAAC,MAAM,CAAC,EAAE0E,SAAS,CAAC,CAAA;AAChE,MAAA,OAAA;AACF,KAAA;IAEA,IAAID,SAAS,CAACG,cAAc,EAAE,IAAIzE,oBAAoB,CAACsE,SAAS,CAAC,EAAE;AACjE,MAAA,MAAM/H,QAAQ,GAAG+H,SAAS,CAACzE,GAAG,CAAC,UAAU,CAAC,CAAA;AAE1C,MAAA,KAAK,IAAI6E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnI,QAAQ,CAACG,MAAM,EAAEgI,CAAC,EAAE,EAAE;QACxCL,kBAAkB,CAACvB,UAAU,EAAEvG,QAAQ,CAACmI,CAAC,CAAC,EAAEH,SAAS,CAAC,CAAA;AACxD,OAAA;AACF,KAAA;IAEA,IAAID,SAAS,CAACK,eAAe,EAAE,IAAI3E,oBAAoB,CAACsE,SAAS,CAAC,EAAE;MAClE,MAAMM,GAAG,GAAG9B,UAAU,CAACf,KAAK,CAACmC,qBAAqB,CAAC,KAAK,CAAC,CAAA;MAEzD,MAAMW,MAAM,GAAGnH,UAAC,CAACoH,mBAAmB,CAAC,KAAK,EAAE,CAC1CpH,UAAC,CAAC2E,kBAAkB,CAACiC,SAAS,CAAClI,IAAI,EAAEwI,GAAG,CAAC,CAC1C,CAAC,CAAA;AAEF,MAAA,IAAIL,SAAS,EAAE;AACbA,QAAAA,SAAS,CAACrD,IAAI,CAAC2D,MAAM,CAAC,CAAA;AACxB,OAAC,MAAM;QACL/B,UAAU,CAACiC,WAAW,EAAE,CAAA;QACvBjC,UAAU,CAACjD,GAAG,CAAC,MAAM,CAAC,CAAgCmF,gBAAgB,CACrE,MAAM,EACNH,MACF,CAAC,CAAA;AACH,OAAA;MACAP,SAAS,CAAChC,WAAW,CAAC5E,UAAC,CAAC4D,SAAS,CAACsD,GAAG,CAAC,CAAC,CAAA;AACzC,KAAA;AACF,GAAA;EAEA,OAAO;AACLxD,IAAAA,IAAI,EAAE,8BAA8B;AACpC6D,IAAAA,iBAAiB,EAEbA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACC,OAAO,CAAClE,IAAI,CAAC,kBAAkB,CAAC;AAE1Db,IAAAA,OAAO,EAAE;MAEPgF,QAAQA,CAAC1F,IAAI,EAAE;AACb,QAAA,MAAM2F,MAAM,GAAG3F,IAAI,CAACE,GAAG,CAAC,QAAQ,CAAC,CAAA;AACjC,QAAA,MAAM0F,qBAAqB,GAAG,IAAIC,GAAG,EAAU,CAAA;AAC/C,QAAA,MAAMC,eAAe,GAAG,IAAID,GAAG,EAAE,CAAA;AACjC,QAAA,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,MAAM,CAAC5I,MAAM,EAAE,EAAEgI,CAAC,EAAE;AACtC,UAAA,MAAMgB,KAAK,GAAGJ,MAAM,CAACZ,CAAC,CAAC,CAAA;AACvB,UAAA,IAAI1E,oBAAoB,CAAC0F,KAAK,CAAC,EAAE;AAC/BH,YAAAA,qBAAqB,CAACI,GAAG,CAACjB,CAAC,CAAC,CAAA;AAC5B,YAAA,KAAK,MAAMtD,IAAI,IAAIsB,MAAM,CAAC9B,IAAI,CAAC8E,KAAK,CAACE,qBAAqB,EAAE,CAAC,EAAE;AAC7DH,cAAAA,eAAe,CAACE,GAAG,CAACvE,IAAI,CAAC,CAAA;AAC3B,aAAA;AACF,WAAA;AACF,SAAA;QAKA,IAAIyE,QAAQ,GAAG,KAAK,CAAA;AAEpB,QAAA,MAAMC,iBAAiB,GAAG,UACxBnG,IAA4B,EAC5BoG,aAAoB,EACpB;AACA,UAAA,MAAM3E,IAAI,GAAGzB,IAAI,CAACvD,IAAI,CAACgF,IAAI,CAAA;UAC3B,IACEzB,IAAI,CAACoC,KAAK,CAACgB,UAAU,CAAC3B,IAAI,CAAC,KAAK2E,aAAa,CAAChD,UAAU,CAAC3B,IAAI,CAAC,IAC9DqE,eAAe,CAACO,GAAG,CAAC5E,IAAI,CAAC,EACzB;AACAyE,YAAAA,QAAQ,GAAG,IAAI,CAAA;YACflG,IAAI,CAACsG,IAAI,EAAE,CAAA;AACb,WAAA;SACD,CAAA;AAED,QAAA,IAAIvB,CAAS,CAAA;AACb,QAAA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,MAAM,CAAC5I,MAAM,IAAI,CAACmJ,QAAQ,EAAE,EAAEnB,CAAC,EAAE;AAC/C,UAAA,MAAMgB,KAAK,GAAGJ,MAAM,CAACZ,CAAC,CAAC,CAAA;AACvB,UAAA,IAAI,CAACa,qBAAqB,CAACS,GAAG,CAACtB,CAAC,CAAC,EAAE;YACjC,IAAIgB,KAAK,CAACQ,sBAAsB,EAAE,IAAIR,KAAK,CAACS,mBAAmB,EAAE,EAAE;AACjEL,cAAAA,iBAAiB,CAACJ,KAAK,EAAE/F,IAAI,CAACoC,KAAK,CAAC,CAAA;AACtC,aAAC,MAAM;cACL2D,KAAK,CAACU,QAAQ,CACZ;AACE,gBAAA,uCAAuC,EAAEzG,IAAI,IAAIA,IAAI,CAAC0G,IAAI,EAAE;AAC5D,gBAAA,wCAAwC,EAAEP,iBAAAA;AAC5C,eAAC,EACDnG,IAAI,CAACoC,KACP,CAAC,CAAA;AACH,aAAA;AACF,WAAA;AACF,SAAA;QAEA,IAAI,CAAC8D,QAAQ,EAAE;AACb,UAAA,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,MAAM,CAAC5I,MAAM,EAAE,EAAEgI,CAAC,EAAE;AACtC,YAAA,MAAMgB,KAAK,GAAGJ,MAAM,CAACZ,CAAC,CAAC,CAAA;AACvB,YAAA,IAAIa,qBAAqB,CAACS,GAAG,CAACtB,CAAC,CAAC,EAAE;AAChCL,cAAAA,kBAAkB,CAAC1E,IAAI,EAAE+F,KAAK,CAAC,CAAA;AACjC,aAAA;AACF,WAAA;AACF,SAAC,MAAM;AACL,UAAA,MAAMY,oBAAoB,GAAIC,GAAW,IACvCA,GAAG,IAAI7B,CAAC,GAAG,CAAC,IAAIa,qBAAqB,CAACS,GAAG,CAACO,GAAG,CAAC,CAAA;UAChDC,+CAAqB,CACnB7G,IAAI,EACJV,oBAAoB,EACpBqH,oBAAoB,EACpBjC,kBACF,CAAC,CAAA;AACH,SAAA;OACD;AAIDoC,MAAAA,kBAAkBA,CAAC9G,IAAI,EAAEJ,IAAI,EAAE;QAC7B,IAAI,CAACI,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,CAAC8E,eAAe,EAAE,EAAE;AACrC,UAAA,OAAA;AACF,SAAA;QAEA,IAAI+B,aAAa,GAAG/G,IAAI,CAAA;QACxB,MAAMgH,YAAY,GAAGhH,IAAI,CAAA;QAEzBS,uBAAuB,CAACT,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,EAAEF,IAAI,IAAI;UAC9C,IAIExD,iCAAiC,CAACwK,YAAY,CAACvK,IAAI,CAAC6H,EAAE,CAAC,IACvD,CAACvG,UAAC,CAACsD,YAAY,CAAC2F,YAAY,CAACvK,IAAI,CAAC+H,IAAI,CAAC,EACvC;AAKA,YAAA,MAAMyC,OAAO,GAAGjH,IAAI,CAACoC,KAAK,CAAC8E,gCAAgC,CACzDF,YAAY,CAACvK,IAAI,CAAC+H,IAAI,EACtB,KACF,CAAC,CAAA;AAEDwC,YAAAA,YAAY,CAACG,YAAY,CACvBpJ,UAAC,CAAC2E,kBAAkB,CAACuE,OAAO,EAAED,YAAY,CAACvK,IAAI,CAAC+H,IAAI,CACtD,CAAC,CAAA;YAEDwC,YAAY,CAACrE,WAAW,CACtB5E,UAAC,CAAC2E,kBAAkB,CAACsE,YAAY,CAACvK,IAAI,CAAC6H,EAAE,EAAEvG,UAAC,CAAC4D,SAAS,CAACsF,OAAO,CAAC,CACjE,CAAC,CAAA;AAED,YAAA,OAAA;AACF,WAAA;AAEA,UAAA,IAAIG,GAAG,GAAGJ,YAAY,CAACvK,IAAI,CAAC+H,IAAI,CAAA;UAChC,MAAM6C,eAA6C,GAAG,EAAE,CAAA;AACxD,UAAA,IAAI5C,IAAI,CAAA;AAERzE,UAAAA,IAAI,CAACqE,UAAU,CAAErE,IAAc,IAAc;AAC3C,YAAA,IAAIA,IAAI,CAACsD,gBAAgB,EAAE,EAAE;AAC3B+D,cAAAA,eAAe,CAACC,OAAO,CAACtH,IAAI,CAAC,CAAA;AAC/B,aAAC,MAAM,IAAIA,IAAI,CAACuH,oBAAoB,EAAE,EAAE;AACtC9C,cAAAA,IAAI,GAAGzE,IAAI,CAACmD,UAAU,CAAC1G,IAAI,CAACgI,IAAI,CAAA;AAChC,cAAA,OAAO,IAAI,CAAA;AACb,aAAA;AACF,WAAC,CAAC,CAAA;UAEF,MAAM+C,+BAA+B,GAAGrF,yBAAyB,CAC/DkF,eAAe,EACfrH,IAAI,CAACoC,KACP,CAAC,CAAA;AACDiF,UAAAA,eAAe,CAACrE,OAAO,CAACnC,IAAI,IAAI;YAC9B,MAAM;AAAEpE,cAAAA,IAAAA;AAAK,aAAC,GAAGoE,IAAI,CAAA;AACrBuG,YAAAA,GAAG,GAAGrJ,UAAC,CAAC8B,gBAAgB,CACtBuH,GAAG,EACHrJ,UAAC,CAAC4D,SAAS,CAAClF,IAAI,CAAC2E,GAAG,CAAC,EACrB3E,IAAI,CAAC6E,QAAQ,IAAIvD,UAAC,CAAC6D,SAAS,CAACnF,IAAI,CAAC2E,GAAG,CACvC,CAAC,CAAA;AACH,WAAC,CAAC,CAAA;AAEF,UAAA,MAAMqG,iBAAiB,GACrBzH,IAAI,CAACmD,UAAuC,CAAA;AAE9C,UAAA,MAAM,CAACd,iCAAiC,EAAEjF,QAAQ,EAAEyG,cAAc,CAAC,GACjEL,gBAAgB,CACdiE,iBAAiB,EACjB7H,IAAI,EACJwH,GACF,CAAC,CAAA;AAEH,UAAA,IAAI3H,WAAW,EAAE;YACfmD,wBAAwB,CAAC6E,iBAAiB,CAAC,CAAA;AAC7C,WAAA;AAEA1J,UAAAA,UAAC,CAAC2J,gBAAgB,CAACtK,QAAQ,CAAC,CAAA;AAE5B2J,UAAAA,aAAa,CAACI,YAAY,CAAC9E,iCAAiC,CAAC,CAAA;AAE7D0E,UAAAA,aAAa,CAACI,YAAY,CAACK,+BAA+B,CAAC,CAAA;AAE3DT,UAAAA,aAAa,GAAGA,aAAa,CAACY,WAAW,CACvC5J,UAAC,CAAC2E,kBAAkB,CAACtF,QAAQ,EAAEyG,cAAc,CAC/C,CAAC,CAAC,CAAC,CAAmC,CAAA;UAEtC7D,IAAI,CAACoC,KAAK,CAACwF,eAAe,CAACnD,IAAI,EAAEsC,aAAa,CAAC,CAAA;UAE/C,IAAIU,iBAAiB,CAAChL,IAAI,CAACO,UAAU,CAACD,MAAM,KAAK,CAAC,EAAE;YAClD0K,iBAAiB,CACdpD,UAAU,CACTrE,IAAI,IAAIA,IAAI,CAACsD,gBAAgB,EAAE,IAAItD,IAAI,CAACuH,oBAAoB,EAC9D,CAAC,CACAhE,MAAM,EAAE,CAAA;AACb,WAAA;AACF,SAAC,CAAC,CAAA;OACH;MAIDsE,sBAAsBA,CAAC7H,IAAI,EAAE;AAC3B,QAAA,MAAM8H,WAAW,GAAG9H,IAAI,CAACE,GAAG,CAAC,aAAa,CAAC,CAAA;AAC3C,QAAA,IAAI,CAAC4H,WAAW,CAACC,qBAAqB,EAAE,EAAE,OAAA;QAE1C,MAAMC,OAAO,GAAGF,WAAW,CACxB5H,GAAG,CAAC,cAAc,CAAC,CACnB+H,IAAI,CAACjI,IAAI,IAAIK,oBAAoB,CAACL,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACrD,IAAI,CAAC8H,OAAO,EAAE,OAAA;QAEd,MAAME,UAAU,GAAG,EAAE,CAAA;AAErB,QAAA,KAAK,MAAMzG,IAAI,IAAIsB,MAAM,CAAC9B,IAAI,CAACjB,IAAI,CAACmI,0BAA0B,CAAC,IAAI,CAAC,CAAC,EAAE;UACrED,UAAU,CAAC3G,IAAI,CACbxD,UAAC,CAACqK,eAAe,CAACrK,UAAC,CAACC,UAAU,CAACyD,IAAI,CAAC,EAAE1D,UAAC,CAACC,UAAU,CAACyD,IAAI,CAAC,CAC1D,CAAC,CAAA;AACH,SAAA;AAKAzB,QAAAA,IAAI,CAAC2C,WAAW,CAACmF,WAAW,CAACrL,IAAI,CAAC,CAAA;QAClCuD,IAAI,CAAC2H,WAAW,CAAC5J,UAAC,CAACsK,sBAAsB,CAAC,IAAI,EAAEH,UAAU,CAAC,CAAC,CAAA;OAC7D;MAGDI,WAAWA,CAACtI,IAAI,EAAE;AAChB,QAAA,MAAM2E,SAAS,GAAG3E,IAAI,CAACE,GAAG,CAAC,OAAO,CAAC,CAAA;AACnCwE,QAAAA,kBAAkB,CAAC1E,IAAI,EAAE2E,SAAS,CAAC,CAAA;OACpC;AAGD4D,MAAAA,oBAAoBA,CAACvI,IAAI,EAAEJ,IAAI,EAAE;AAC/B,QAAA,MAAM4I,QAAQ,GAAGxI,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC,CAAA;QACjC,IAAIsI,QAAQ,CAACxD,eAAe,EAAE,IAAI3E,oBAAoB,CAACmI,QAAQ,CAAC,EAAE;UAChE,MAAMC,KAAK,GAAG,EAAE,CAAA;AAEhB,UAAA,MAAMC,OAAO,GAAG1I,IAAI,CAACoC,KAAK,CAACI,sBAAsB,CAC/CxC,IAAI,CAACvD,IAAI,CAACkM,KAAK,EACf,KACF,CAAC,CAAA;AAEDF,UAAAA,KAAK,CAAClH,IAAI,CACRxD,UAAC,CAACoH,mBAAmB,CAAC,KAAK,EAAE,CAC3BpH,UAAC,CAAC2E,kBAAkB,CAAC3E,UAAC,CAACC,UAAU,CAAC0K,OAAO,CAAC,EAAE1I,IAAI,CAACvD,IAAI,CAACkM,KAAK,CAAC,CAC7D,CACH,CAAC,CAAA;UAED,MAAM,CAACtG,iCAAiC,EAAEjF,QAAQ,EAAEyG,cAAc,CAAC,GACjEL,gBAAgB,CAACgF,QAAQ,EAAE5I,IAAI,EAAE7B,UAAC,CAACC,UAAU,CAAC0K,OAAO,CAAC,CAAC,CAAA;AAEzD,UAAA,IAAIrG,iCAAiC,CAACtF,MAAM,GAAG,CAAC,EAAE;YAChD0L,KAAK,CAAClH,IAAI,CACRxD,UAAC,CAACoH,mBAAmB,CAAC,KAAK,EAAE9C,iCAAiC,CAChE,CAAC,CAAA;AACH,WAAA;UAEA,MAAMuG,iBAAiB,GAAG7K,UAAC,CAAC4D,SAAS,CAAC3B,IAAI,CAACvD,IAAI,CAAC,CAAA;UAChDmM,iBAAiB,CAACD,KAAK,GAAG5K,UAAC,CAACC,UAAU,CAAC0K,OAAO,CAAC,CAAA;UAC/CD,KAAK,CAAClH,IAAI,CAACxD,UAAC,CAAC8K,mBAAmB,CAACD,iBAAiB,CAAC,CAAC,CAAA;AACpDH,UAAAA,KAAK,CAAClH,IAAI,CACRxD,UAAC,CAAC8K,mBAAmB,CACnB9K,UAAC,CAAC+K,oBAAoB,CAAC,GAAG,EAAE1L,QAAQ,EAAEyG,cAAc,CACtD,CACF,CAAC,CAAA;AACD4E,UAAAA,KAAK,CAAClH,IAAI,CAACxD,UAAC,CAAC8K,mBAAmB,CAAC9K,UAAC,CAACC,UAAU,CAAC0K,OAAO,CAAC,CAAC,CAAC,CAAA;AAExD1I,UAAAA,IAAI,CAAC+I,mBAAmB,CAACN,KAAK,CAAC,CAAA;AACjC,SAAA;OACD;MAGDO,aAAaA,CAAChJ,IAA+B,EAAE;QAC7C,MAAM;UAAEvD,IAAI;AAAE2F,UAAAA,KAAAA;AAAM,SAAC,GAAGpC,IAAI,CAAA;AAC5B,QAAA,MAAMwI,QAAQ,GAAGxI,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC,CAAA;AAEjC,QAAA,IAAI,CAACsI,QAAQ,CAACT,qBAAqB,EAAE,EAAE;AACrC,UAAA,IAAI,CAAC1H,oBAAoB,CAACmI,QAAQ,CAAC,EAAE;AACnC,YAAA,OAAA;AACF,WAAA;AAEA,UAAA,MAAMS,IAAI,GAAG7G,KAAK,CAACmC,qBAAqB,CAAC,KAAK,CAAC,CAAA;AAE/C9H,UAAAA,IAAI,CAACU,IAAI,GAAGY,UAAC,CAACoH,mBAAmB,CAAC,KAAK,EAAE,CACvCpH,UAAC,CAAC2E,kBAAkB,CAACuG,IAAI,CAAC,CAC3B,CAAC,CAAA;UAEFjJ,IAAI,CAACoF,WAAW,EAAE,CAAA;UAElB,MAAM8D,aAAa,GAAIlJ,IAAI,CAACvD,IAAI,CAAC0M,IAAI,CAAsBA,IAAI,CAAA;UAC/D,MAAMV,KAAK,GAAG,EAAE,CAAA;UAKhB,IAAIS,aAAa,CAACnM,MAAM,KAAK,CAAC,IAAIiD,IAAI,CAACoJ,kBAAkB,EAAE,EAAE;AAC3DX,YAAAA,KAAK,CAACnB,OAAO,CAACvJ,UAAC,CAAC8K,mBAAmB,CAACzG,KAAK,CAACiH,kBAAkB,EAAE,CAAC,CAAC,CAAA;AAClE,WAAA;UAEAZ,KAAK,CAACnB,OAAO,CACXvJ,UAAC,CAAC8K,mBAAmB,CACnB9K,UAAC,CAAC+K,oBAAoB,CAAC,GAAG,EAAEN,QAAQ,CAAC/L,IAAI,EAAEsB,UAAC,CAAC4D,SAAS,CAACsH,IAAI,CAAC,CAC9D,CACF,CAAC,CAAA;AAEDK,UAAAA,qDAAwB,CAACtJ,IAAI,EAAEyI,KAAK,CAAC,CAAA;UACrCrG,KAAK,CAACmH,KAAK,EAAE,CAAA;AACb,UAAA,OAAA;AACF,SAAC,MAAM;AAEL,UAAA,MAAMC,WAAW,GAAGhB,QAAQ,CAACtI,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAACA,GAAG,CAAC,IAAI,CAAC,CAAA;AAC7D,UAAA,IAAI,CAACG,oBAAoB,CAACmJ,WAAW,CAAC,EAAE;AACtC,YAAA,OAAA;AACF,WAAA;AACA,UAAA,MAAMrM,IAAI,GAAGqL,QAAQ,CAAC/L,IAAI,CAAA;AAC1B,UAAA,MAAM0B,OAAO,GAAGqL,WAAW,CAAC/M,IAAI,CAAA;AAEhC,UAAA,MAAM2E,GAAG,GAAGgB,KAAK,CAACmC,qBAAqB,CAAC,KAAK,CAAC,CAAA;UAC9C9H,IAAI,CAACU,IAAI,GAAGY,UAAC,CAACoH,mBAAmB,CAAChI,IAAI,CAACsH,IAAI,EAAE,CAC3C1G,UAAC,CAAC2E,kBAAkB,CAACtB,GAAG,EAAE,IAAI,CAAC,CAChC,CAAC,CAAA;UAEFpB,IAAI,CAACoF,WAAW,EAAE,CAAA;AAElBkE,UAAAA,qDAAwB,CAACtJ,IAAI,EAAE,CAC7BjC,UAAC,CAACoH,mBAAmB,CAAC1I,IAAI,CAACU,IAAI,CAACsH,IAAI,EAAE,CACpC1G,UAAC,CAAC2E,kBAAkB,CAACvE,OAAO,EAAEJ,UAAC,CAAC4D,SAAS,CAACP,GAAG,CAAC,CAAC,CAChD,CAAC,CACH,CAAC,CAAA;UACFgB,KAAK,CAACmH,KAAK,EAAE,CAAA;AACb,UAAA,OAAA;AACF,SAAA;OACD;MAGDE,YAAYA,CAACzJ,IAAI,EAAE;QAEjB,MAAM0J,cAA2B,GAAG,EAAE,CAAA;QACtC,MAAM;AAAEtH,UAAAA,KAAAA;AAAM,SAAC,GAAGpC,IAAI,CAAA;QACtB,MAAM2J,cAA8B,GAAG,EAAE,CAAA;AAEzClJ,QAAAA,uBAAuB,CAACT,IAAI,EAAEA,IAAI,IAAI;AACpC,UAAA,MAAM5B,aAAa,GAAG4B,IAAI,CAACmD,UAAuC,CAAA;AAElE,UAAA,MAAM8B,GAAG,GAAG7C,KAAK,CAACmC,qBAAqB,CAAC,KAAK,CAAC,CAAA;UAC9CmF,cAAc,CAACnI,IAAI,CAAC;YAAEpE,IAAI,EAAEiB,aAAa,CAAC3B,IAAI;AAAEkM,YAAAA,KAAK,EAAE1D,GAAAA;AAAI,WAAC,CAAC,CAAA;AAC7D0E,UAAAA,cAAc,CAACpI,IAAI,CAAC0D,GAAG,CAAC,CAAA;UAExB7G,aAAa,CAACuE,WAAW,CAAC5E,UAAC,CAAC4D,SAAS,CAACsD,GAAG,CAAC,CAAC,CAAA;UAC3CjF,IAAI,CAAC0G,IAAI,EAAE,CAAA;AACb,SAAC,CAAC,CAAA;AAEF,QAAA,IAAIgD,cAAc,CAAC3M,MAAM,GAAG,CAAC,EAAE;UAC7B,MAAM6M,iBAAiB,GAAG5J,IAAI,CAACqE,UAAU,CACvCrE,IAAI,IAAI,EAAEA,IAAI,CAAC6J,SAAS,EAAE,IAAI7J,IAAI,CAACsD,gBAAgB,EAAE,CACvD,CAAC,CAAA;AACD,UAAA,MAAMwG,aAAa,GAAGF,iBAAiB,CAACnN,IAAI,CAAA;UAC5C,QAAQqN,aAAa,CAACpN,IAAI;AACxB,YAAA,KAAK,oBAAoB;AACvBkN,cAAAA,iBAAiB,CAACjC,WAAW,CAC3B+B,cAAc,CAACK,GAAG,CAAC,CAAC;gBAAE5M,IAAI;AAAEwL,gBAAAA,KAAAA;eAAO,KACjC5K,UAAC,CAAC2E,kBAAkB,CAACvF,IAAI,EAAEwL,KAAK,CAClC,CACF,CAAC,CAAA;AACD,cAAA,MAAA;AACF,YAAA,KAAK,sBAAsB;AACzB,cAAA;AACE,gBAAA,KAAK,MAAMqB,aAAa,IAAIL,cAAc,EAAE;kBAC1CvH,KAAK,CAACb,IAAI,CAAC;AAAE+C,oBAAAA,EAAE,EAAEvG,UAAC,CAAC4D,SAAS,CAACqI,aAAa,CAAA;AAAE,mBAAC,CAAC,CAAA;AAChD,iBAAA;AACAJ,gBAAAA,iBAAiB,CAACjC,WAAW,CAC3B+B,cAAc,CAACK,GAAG,CAAC,CAAC;kBAAE5M,IAAI;AAAEwL,kBAAAA,KAAAA;AAAM,iBAAC,KACjC5K,UAAC,CAAC+K,oBAAoB,CAAC,GAAG,EAAE3L,IAAI,EAAEwL,KAAK,CACzC,CACF,CAAC,CAAA;AACH,eAAA;AACA,cAAA,MAAA;AACF,YAAA;cACE,MAAM,IAAItJ,KAAK,CACb,CAAA,gCAAA,EAAmCyK,aAAa,CAACpN,IAAI,EACvD,CAAC,CAAA;AACL,WAAA;AACF,SAAA;OACD;AAGDuN,MAAAA,gBAAgBA,CAACjK,IAAI,EAAEJ,IAAI,EAAE;AAC3B,QAAA,IAAI,CAACgB,SAAS,CAACZ,IAAI,CAACvD,IAAI,CAAC,EAAE,OAAA;AAE3B,QAAA,IAAIyN,MAAyC,CAAA;AAC7C,QAAA,IAAIxK,mBAAmB,EAAE;AACvBwK,UAAAA,MAAM,GAAGvK,gBAAgB,CAACC,IAAI,CAAC,CAAA;AACjC,SAAC,MAAM;AAGE,UAAA;YACL,IAAI;AACFsK,cAAAA,MAAM,GAAGtK,IAAI,CAACE,SAAS,CAAC,eAAe,CAAC,CAAA;aACzC,CAAC,OAAAqK,OAAA,EAAM;AAIN,cAAA,IAAI,CAACvK,IAAI,CAACwK,YAAY,CAACC,aAAa,GAAG,IAAI,CAAA;AAI3CH,cAAAA,MAAM,GAAGtK,IAAI,CAACE,SAAS,CAAC,cAAc,CAAC,CAAA;AACzC,aAAA;AACF,WAAA;AACF,SAAA;QAEA,IAAIwK,GAAqB,GAAG,IAAI,CAAA;QAChC,IAAItJ,KAAuB,GAAG,EAAE,CAAA;QAEhC,SAASuJ,IAAIA,GAAG;AACd,UAAA,MAAMC,QAAQ,GAAGxJ,KAAK,CAACjE,MAAM,GAAG,CAAC,CAAA;AACjC,UAAA,MAAM0N,GAAG,GAAG1M,UAAC,CAAC+F,gBAAgB,CAAC9C,KAAK,CAAC,CAAA;AACrCA,UAAAA,KAAK,GAAG,EAAE,CAAA;UAEV,IAAI,CAACsJ,GAAG,EAAE;YACRA,GAAG,GAAGvM,UAAC,CAAC8F,cAAc,CAACqG,MAAM,EAAE,CAACO,GAAG,CAAC,CAAC,CAAA;AACrC,YAAA,OAAA;AACF,WAAA;AAIA,UAAA,IAAIhL,WAAW,EAAE;AACf,YAAA,IAAI+K,QAAQ,EAAE;AACZF,cAAAA,GAAG,CAACI,SAAS,CAACnJ,IAAI,CAACkJ,GAAG,CAAC,CAAA;AACzB,aAAA;AACA,YAAA,OAAA;AACF,WAAA;AAEAH,UAAAA,GAAG,GAAGvM,UAAC,CAAC8F,cAAc,CAAC9F,UAAC,CAAC4D,SAAS,CAACuI,MAAM,CAAC,EAAE,CAC1CI,GAAG,EAIH,IAAIE,QAAQ,GAAG,CAACzM,UAAC,CAAC+F,gBAAgB,CAAC,EAAE,CAAC,EAAE2G,GAAG,CAAC,GAAG,EAAE,CAAC,CACnD,CAAC,CAAA;AACJ,SAAA;QAEA,KAAK,MAAM5J,IAAI,IAAIb,IAAI,CAACvD,IAAI,CAACO,UAAU,EAAE;AACvC,UAAA,IAAIe,UAAC,CAAC+C,eAAe,CAACD,IAAI,CAAC,EAAE;AAC3B0J,YAAAA,IAAI,EAAE,CAAA;YACND,GAAG,CAACI,SAAS,CAACnJ,IAAI,CAACV,IAAI,CAACzD,QAAQ,CAAC,CAAA;AACnC,WAAC,MAAM;AACL4D,YAAAA,KAAK,CAACO,IAAI,CAACV,IAAI,CAAC,CAAA;AAClB,WAAA;AACF,SAAA;AAEA,QAAA,IAAIG,KAAK,CAACjE,MAAM,EAAEwN,IAAI,EAAE,CAAA;AAExBvK,QAAAA,IAAI,CAAC2C,WAAW,CAAC2H,GAAG,CAAC,CAAA;AACvB,OAAA;AACF,KAAA;GACD,CAAA;AACH,CAAC,CAAC;;;;"}
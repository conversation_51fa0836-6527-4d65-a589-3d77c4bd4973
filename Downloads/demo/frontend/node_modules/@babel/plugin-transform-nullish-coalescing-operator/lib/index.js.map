{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "loose", "_api$assumption", "_api$assumption2", "assertVersion", "noDocumentAll", "assumption", "pureGetters", "name", "manipulateOptions", "_", "parser", "plugins", "push", "visitor", "LogicalExpression", "path", "node", "scope", "operator", "ref", "assignment", "isPattern", "t", "isMemberExpression", "left", "computed", "isIdentifier", "object", "property", "hasBinding", "cloneNode", "replaceWith", "template", "statement", "ast", "generateUidIdentifierBasedOnNode", "id", "assignmentExpression", "conditionalExpression", "binaryExpression", "nullLiteral", "logicalExpression", "buildUndefinedNode", "right"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t, template } from \"@babel/core\";\n\nexport interface Options {\n  loose?: boolean;\n}\n\nexport default declare((api, { loose = false }: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n  const noDocumentAll = api.assumption(\"noDocumentAll\") ?? loose;\n  const pureGetters = api.assumption(\"pureGetters\") ?? false;\n\n  return {\n    name: \"transform-nullish-coalescing-operator\",\n    manipulateOptions: process.env.BABEL_8_BREAKING\n      ? undefined\n      : (_, parser) => parser.plugins.push(\"nullishCoalescingOperator\"),\n\n    visitor: {\n      LogicalExpression(path) {\n        const { node, scope } = path;\n        if (node.operator !== \"??\") {\n          return;\n        }\n\n        let ref;\n        let assignment;\n        // skip creating extra reference when `left` is pure\n        if (\n          (pureGetters &&\n            scope.path.isPattern() &&\n            t.isMemberExpression(node.left) &&\n            !node.left.computed &&\n            t.isIdentifier(node.left.object) &&\n            t.isIdentifier(node.left.property)) ||\n          (t.isIdentifier(node.left) &&\n            (pureGetters ||\n              // globalThis\n              scope.hasBinding(node.left.name)))\n        ) {\n          ref = node.left;\n          assignment = t.cloneNode(node.left);\n        } else if (scope.path.isPattern()) {\n          // Replace `function (a, x = a.b ?? c) {}` to `function (a, x = (() => a.b ?? c)() ){}`\n          // so the temporary variable can be injected in correct scope\n          path.replaceWith(template.statement.ast`(() => ${path.node})()`);\n          // The injected nullish expression will be queued and eventually transformed when visited\n          return;\n        } else {\n          ref = scope.generateUidIdentifierBasedOnNode(node.left);\n          scope.push({ id: t.cloneNode(ref) });\n          assignment = t.assignmentExpression(\"=\", ref, node.left);\n        }\n\n        path.replaceWith(\n          t.conditionalExpression(\n            // We cannot use `!= null` in spec mode because\n            // `document.all == null` and `document.all` is not \"nullish\".\n            noDocumentAll\n              ? t.binaryExpression(\"!=\", assignment, t.nullLiteral())\n              : t.logicalExpression(\n                  \"&&\",\n                  t.binaryExpression(\"!==\", assignment, t.nullLiteral()),\n                  t.binaryExpression(\n                    \"!==\",\n                    t.cloneNode(ref),\n                    scope.buildUndefinedNode(),\n                  ),\n                ),\n            t.cloneNode(ref),\n            node.right,\n          ),\n        );\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAmD,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAMpC,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAE;EAAEC,KAAK,GAAG;AAAe,CAAC,KAAK;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EAC1DH,GAAG,CAACI,aAAa,uCAAoB,CAAC;EACtC,MAAMC,aAAa,IAAAH,eAAA,GAAGF,GAAG,CAACM,UAAU,CAAC,eAAe,CAAC,YAAAJ,eAAA,GAAID,KAAK;EAC9D,MAAMM,WAAW,IAAAJ,gBAAA,GAAGH,GAAG,CAACM,UAAU,CAAC,aAAa,CAAC,YAAAH,gBAAA,GAAI,KAAK;EAE1D,OAAO;IACLK,IAAI,EAAE,uCAAuC;IAC7CC,iBAAiB,EAEbA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,2BAA2B,CAAC;IAEnEC,OAAO,EAAE;MACPC,iBAAiBA,CAACC,IAAI,EAAE;QACtB,MAAM;UAAEC,IAAI;UAAEC;QAAM,CAAC,GAAGF,IAAI;QAC5B,IAAIC,IAAI,CAACE,QAAQ,KAAK,IAAI,EAAE;UAC1B;QACF;QAEA,IAAIC,GAAG;QACP,IAAIC,UAAU;QAEd,IACGd,WAAW,IACVW,KAAK,CAACF,IAAI,CAACM,SAAS,CAAC,CAAC,IACtBC,WAAC,CAACC,kBAAkB,CAACP,IAAI,CAACQ,IAAI,CAAC,IAC/B,CAACR,IAAI,CAACQ,IAAI,CAACC,QAAQ,IACnBH,WAAC,CAACI,YAAY,CAACV,IAAI,CAACQ,IAAI,CAACG,MAAM,CAAC,IAChCL,WAAC,CAACI,YAAY,CAACV,IAAI,CAACQ,IAAI,CAACI,QAAQ,CAAC,IACnCN,WAAC,CAACI,YAAY,CAACV,IAAI,CAACQ,IAAI,CAAC,KACvBlB,WAAW,IAEVW,KAAK,CAACY,UAAU,CAACb,IAAI,CAACQ,IAAI,CAACjB,IAAI,CAAC,CAAE,EACtC;UACAY,GAAG,GAAGH,IAAI,CAACQ,IAAI;UACfJ,UAAU,GAAGE,WAAC,CAACQ,SAAS,CAACd,IAAI,CAACQ,IAAI,CAAC;QACrC,CAAC,MAAM,IAAIP,KAAK,CAACF,IAAI,CAACM,SAAS,CAAC,CAAC,EAAE;UAGjCN,IAAI,CAACgB,WAAW,CAACC,cAAQ,CAACC,SAAS,CAACC,GAAG,UAAUnB,IAAI,CAACC,IAAI,KAAK,CAAC;UAEhE;QACF,CAAC,MAAM;UACLG,GAAG,GAAGF,KAAK,CAACkB,gCAAgC,CAACnB,IAAI,CAACQ,IAAI,CAAC;UACvDP,KAAK,CAACL,IAAI,CAAC;YAAEwB,EAAE,EAAEd,WAAC,CAACQ,SAAS,CAACX,GAAG;UAAE,CAAC,CAAC;UACpCC,UAAU,GAAGE,WAAC,CAACe,oBAAoB,CAAC,GAAG,EAAElB,GAAG,EAAEH,IAAI,CAACQ,IAAI,CAAC;QAC1D;QAEAT,IAAI,CAACgB,WAAW,CACdT,WAAC,CAACgB,qBAAqB,CAGrBlC,aAAa,GACTkB,WAAC,CAACiB,gBAAgB,CAAC,IAAI,EAAEnB,UAAU,EAAEE,WAAC,CAACkB,WAAW,CAAC,CAAC,CAAC,GACrDlB,WAAC,CAACmB,iBAAiB,CACjB,IAAI,EACJnB,WAAC,CAACiB,gBAAgB,CAAC,KAAK,EAAEnB,UAAU,EAAEE,WAAC,CAACkB,WAAW,CAAC,CAAC,CAAC,EACtDlB,WAAC,CAACiB,gBAAgB,CAChB,KAAK,EACLjB,WAAC,CAACQ,SAAS,CAACX,GAAG,CAAC,EAChBF,KAAK,CAACyB,kBAAkB,CAAC,CAC3B,CACF,CAAC,EACLpB,WAAC,CAACQ,SAAS,CAACX,GAAG,CAAC,EAChBH,IAAI,CAAC2B,KACP,CACF,CAAC;MACH;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}
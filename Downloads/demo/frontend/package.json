{"name": "comic-reader-frontend", "version": "1.0.0", "description": "漫画阅读器前端应用", "main": "src/main.js", "scripts": {"serve": "webpack serve --mode development", "build": "webpack --mode production", "dev": "webpack serve --mode development --open", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.9.0", "element-ui": "^2.15.14", "vue": "^2.6.14", "vue-router": "^3.6.5"}, "devDependencies": {"@babel/core": "^7.27.3", "@babel/preset-env": "^7.27.2", "babel-loader": "^10.0.0", "css-loader": "^7.1.2", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "style-loader": "^4.0.0", "url-loader": "^4.1.1", "vue-loader": "^17.4.2", "vue-template-compiler": "^2.7.16", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>漫画阅读器</title>
  <!-- ElementUI CSS -->
  <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
      background-color: #f5f5f5;
    }
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      text-align: center;
      padding: 40px 20px;
    }
    .header h1 {
      margin: 0 0 10px 0;
      font-size: 2.5em;
      font-weight: 300;
    }
    .header p {
      margin: 0;
      font-size: 1.2em;
      opacity: 0.9;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    .comic-card {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      transition: all 0.3s;
      cursor: pointer;
      margin-bottom: 20px;
    }
    .comic-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
    }
    .comic-cover {
      width: 100%;
      height: 200px;
      object-fit: cover;
      border-radius: 8px 8px 0 0;
      background-color: #f5f5f5;
      transition: opacity 0.3s ease;
      display: block;
    }

    .comic-cover:not([src]) {
      opacity: 0.7;
    }
    .comic-info {
      padding: 15px;
    }
    .comic-title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 8px;
    }
    .comic-description {
      font-size: 14px;
      color: #606266;
      line-height: 1.5;
    }
    .comic-author {
      font-size: 12px;
      color: #909399;
      margin-top: 8px;
    }
    .loading {
      text-align: center;
      padding: 40px;
    }
    .error {
      text-align: center;
      padding: 40px;
      color: #f56c6c;
    }
  </style>
</head>
<body>
  <div id="app">
    <!-- 头部 -->
    <div class="header">
      <h1>漫画阅读器</h1>
      <p>发现精彩的漫画世界</p>
      <div style="margin-top: 20px;">
        <el-switch
          v-model="useMockData"
          @change="loadComics"
          active-text="Mock数据"
          inactive-text="真实API">
        </el-switch>
        <el-tag :type="useMockData ? 'success' : 'warning'" style="margin-left: 10px;">
          {{ useMockData ? '演示模式' : '实际数据' }}
        </el-tag>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <i class="el-icon-loading"></i>
        <p>加载中...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error">
        <i class="el-icon-warning"></i>
        <p>{{ error }}</p>
        <el-button @click="loadComics" type="primary">重新加载</el-button>
      </div>

      <!-- 漫画列表 -->
      <div v-else>
        <el-row :gutter="20">
          <el-col
            v-for="comic in comics"
            :key="comic.id"
            :xs="12"
            :sm="8"
            :md="6"
            :lg="4"
          >
            <div class="comic-card" @click="goToDetail(comic.id)">
              <img
                :src="getImageUrl(comic.cover_image)"
                :alt="comic.title"
                class="comic-cover"
                @error="handleImageError"
              />
              <div class="comic-info">
                <div class="comic-title">{{ comic.title }}</div>
                <div class="comic-description">{{ comic.description || '暂无描述' }}</div>
                <div class="comic-author">
                  <span>作者：{{ comic.author || '未知' }}</span>
                  <el-tag
                    :type="comic.status === 'completed' ? 'success' : 'primary'"
                    size="mini"
                    style="float: right;">
                    {{ comic.status === 'completed' ? '已完结' : '连载中' }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>

  <!-- Vue.js -->
  <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
  <!-- ElementUI JS -->
  <script src="https://unpkg.com/element-ui/lib/index.js"></script>
  <!-- Axios -->
  <script src="https://unpkg.com/axios/dist/axios.min.js"></script>

  <script>
    new Vue({
      el: '#app',
      data: {
        comics: [],
        loading: false,
        error: null,
        useMockData: true // 启用mock数据
      },
      created() {
        this.loadComics();
      },
      methods: {
        async loadComics() {
          this.loading = true;
          this.error = null;

          if (this.useMockData) {
            // 使用模拟数据
            setTimeout(() => {
              this.comics = this.getMockData();
              this.loading = false;
            }, 1000); // 模拟加载时间
            return;
          }

          try {
            const response = await axios.get('http://localhost:3000/api/comics');
            if (response.data.success) {
              this.comics = response.data.data;
            } else {
              this.error = response.data.message || '获取漫画列表失败';
            }
          } catch (error) {
            console.error('加载漫画列表失败:', error);
            this.error = '网络错误，请检查后端服务是否启动';
          } finally {
            this.loading = false;
          }
        },

        getMockData() {
          return [
            {
              id: 1,
              title: "进击的巨人",
              description: "在这个世界上，人类居住在由三重巨大的城墙所围成的都市里。在城墙外面，有着似乎会捕食人类的巨人类生物，被称为「巨人」。",
              author: "谏山创",
              cover_image: "https://picsum.photos/300/400?random=1",
              status: "completed"
            },
            {
              id: 2,
              title: "鬼灭之刃",
              description: "大正时代，卖炭少年炭治郎的平凡生活在家人遭到鬼杀害后发生了变化。为了让变成鬼的妹妹祢豆子恢复人形，炭治郎踏上了成为鬼杀队队员的道路。",
              author: "吾峠呼世晴",
              cover_image: "https://picsum.photos/300/400?random=2",
              status: "completed"
            },
            {
              id: 3,
              title: "海贼王",
              description: "路飞为了成为海贼王，与伙伴们一起在伟大航路上冒险的故事。充满友情、梦想与冒险的热血漫画。",
              author: "尾田荣一郎",
              cover_image: "https://picsum.photos/300/400?random=3",
              status: "ongoing"
            },
            {
              id: 4,
              title: "火影忍者",
              description: "漩涡鸣人是木叶隐村的忍者，梦想是成为火影。讲述了他与伙伴们一起成长，保护村子的故事。",
              author: "岸本齐史",
              cover_image: "https://picsum.photos/300/400?random=4",
              status: "completed"
            },
            {
              id: 5,
              title: "龙珠",
              description: "孙悟空寻找龙珠的冒险故事，从地球到宇宙，不断变强的战斗历程。",
              author: "鸟山明",
              cover_image: "https://picsum.photos/300/400?random=5",
              status: "completed"
            },
            {
              id: 6,
              title: "死神",
              description: "黑崎一护获得死神力量后，与虚战斗，保护人类世界的故事。",
              author: "久保带人",
              cover_image: "https://picsum.photos/300/400?random=6",
              status: "completed"
            },
            {
              id: 7,
              title: "咒术回战",
              description: "虎杖悠仁吞下诅咒之王两面宿傩的手指后，进入东京咒术高等专门学校学习咒术的故事。",
              author: "芥见下下",
              cover_image: "https://picsum.photos/300/400?random=7",
              status: "ongoing"
            },
            {
              id: 8,
              title: "我的英雄学院",
              description: "在一个80%的人都拥有超能力「个性」的世界里，无个性少年绿谷出久追求成为英雄的故事。",
              author: "堀越耕平",
              cover_image: "https://picsum.photos/300/400?random=8",
              status: "ongoing"
            }
          ];
        },

        goToDetail(comicId) {
          // 跳转到漫画详情页
          window.location.href = `detail.html?id=${comicId}`;
        },

        getImageUrl(imagePath) {
          if (!imagePath) {
            return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaaguaXoOWwgemdojwvdGV4dD48L3N2Zz4=';
          }
          return `http://localhost:3000${imagePath}`;
        },

        handleImageError(event) {
          event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD48L3N2Zz4=';
        }
      }
    });
  </script>
</body>
</html>

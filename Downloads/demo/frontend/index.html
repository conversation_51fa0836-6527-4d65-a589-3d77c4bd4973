<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>漫画阅读器</title>
  <!-- ElementUI CSS -->
  <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
      background-color: #f5f5f5;
    }
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      text-align: center;
      padding: 40px 20px;
    }
    .header h1 {
      margin: 0 0 10px 0;
      font-size: 2.5em;
      font-weight: 300;
    }
    .header p {
      margin: 0;
      font-size: 1.2em;
      opacity: 0.9;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    .comic-card {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      transition: all 0.3s;
      cursor: pointer;
      margin-bottom: 20px;
    }
    .comic-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
    }
    .comic-cover {
      width: 100%;
      height: 200px;
      object-fit: cover;
      border-radius: 8px 8px 0 0;
    }
    .comic-info {
      padding: 15px;
    }
    .comic-title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 8px;
    }
    .comic-description {
      font-size: 14px;
      color: #606266;
      line-height: 1.5;
    }
    .comic-author {
      font-size: 12px;
      color: #909399;
      margin-top: 8px;
    }
    .loading {
      text-align: center;
      padding: 40px;
    }
    .error {
      text-align: center;
      padding: 40px;
      color: #f56c6c;
    }
  </style>
</head>
<body>
  <div id="app">
    <!-- 头部 -->
    <div class="header">
      <h1>漫画阅读器</h1>
      <p>发现精彩的漫画世界</p>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <i class="el-icon-loading"></i>
        <p>加载中...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error">
        <i class="el-icon-warning"></i>
        <p>{{ error }}</p>
        <el-button @click="loadComics" type="primary">重新加载</el-button>
      </div>

      <!-- 漫画列表 -->
      <div v-else>
        <el-row :gutter="20">
          <el-col 
            v-for="comic in comics" 
            :key="comic.id" 
            :xs="12" 
            :sm="8" 
            :md="6" 
            :lg="4"
          >
            <div class="comic-card" @click="goToDetail(comic.id)">
              <img 
                :src="getImageUrl(comic.cover_image)" 
                :alt="comic.title"
                class="comic-cover"
                @error="handleImageError"
              />
              <div class="comic-info">
                <div class="comic-title">{{ comic.title }}</div>
                <div class="comic-description">{{ comic.description || '暂无描述' }}</div>
                <div class="comic-author">作者：{{ comic.author || '未知' }}</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>

  <!-- Vue.js -->
  <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
  <!-- ElementUI JS -->
  <script src="https://unpkg.com/element-ui/lib/index.js"></script>
  <!-- Axios -->
  <script src="https://unpkg.com/axios/dist/axios.min.js"></script>

  <script>
    new Vue({
      el: '#app',
      data: {
        comics: [],
        loading: false,
        error: null
      },
      created() {
        this.loadComics();
      },
      methods: {
        async loadComics() {
          this.loading = true;
          this.error = null;
          
          try {
            const response = await axios.get('http://localhost:3000/api/comics');
            if (response.data.success) {
              this.comics = response.data.data;
            } else {
              this.error = response.data.message || '获取漫画列表失败';
            }
          } catch (error) {
            console.error('加载漫画列表失败:', error);
            this.error = '网络错误，请检查后端服务是否启动';
          } finally {
            this.loading = false;
          }
        },
        
        goToDetail(comicId) {
          // 简单的页面跳转，实际项目中会使用路由
          window.location.href = `detail.html?id=${comicId}`;
        },
        
        getImageUrl(imagePath) {
          if (!imagePath) {
            return 'https://via.placeholder.com/200x200?text=暂无封面';
          }
          return `http://localhost:3000${imagePath}`;
        },
        
        handleImageError(event) {
          event.target.src = 'https://via.placeholder.com/200x200?text=图片加载失败';
        }
      }
    });
  </script>
</body>
</html>

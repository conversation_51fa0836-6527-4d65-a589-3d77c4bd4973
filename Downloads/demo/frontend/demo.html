<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>演示说明 - 漫画阅读器</title>
  <!-- ElementUI CSS -->
  <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
      background-color: #f5f5f5;
    }
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      text-align: center;
      padding: 40px 20px;
    }
    .container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
    }
    .demo-card {
      margin-bottom: 20px;
    }
    .feature-list {
      list-style: none;
      padding: 0;
    }
    .feature-list li {
      padding: 8px 0;
      border-bottom: 1px solid #eee;
    }
    .feature-list li:last-child {
      border-bottom: none;
    }
    .demo-button {
      margin: 10px;
    }
    .tech-stack {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 15px;
    }
  </style>
</head>
<body>
  <div id="app">
    <!-- 头部 -->
    <div class="header">
      <h1>🎨 漫画阅读器演示</h1>
      <p>基于 Vue2 + ElementUI + Node.js + Express + MySQL 的完整解决方案</p>
    </div>

    <div class="container">
      <!-- 功能演示 -->
      <el-card class="demo-card">
        <div slot="header">
          <span>🚀 功能演示</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-button type="primary" size="medium" class="demo-button" @click="goToPage('index.html')">
              <i class="el-icon-collection"></i> 漫画列表
            </el-button>
            <p>展示所有漫画，支持Mock数据和真实API切换</p>
          </el-col>
          <el-col :span="8">
            <el-button type="success" size="medium" class="demo-button" @click="goToPage('detail.html?id=1')">
              <i class="el-icon-document"></i> 漫画详情
            </el-button>
            <p>查看漫画详细信息和章节列表</p>
          </el-col>
          <el-col :span="8">
            <el-button type="warning" size="medium" class="demo-button" @click="goToPage('reader.html?chapterId=1')">
              <i class="el-icon-view"></i> 阅读器
            </el-button>
            <p>沉浸式阅读体验，支持翻页和章节导航</p>
          </el-col>
        </el-row>
      </el-card>

      <!-- 技术特性 -->
      <el-card class="demo-card">
        <div slot="header">
          <span>✨ 技术特性</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>前端特性</h4>
            <ul class="feature-list">
              <li>✅ Vue2 + ElementUI 响应式界面</li>
              <li>✅ 组件化开发，代码复用性高</li>
              <li>✅ 支持Mock数据和真实API切换</li>
              <li>✅ 移动端适配，触摸友好</li>
              <li>✅ 键盘快捷键支持</li>
              <li>✅ 图片懒加载和错误处理</li>
              <li>✅ 加载状态和错误提示</li>
            </ul>
          </el-col>
          <el-col :span="12">
            <h4>后端特性</h4>
            <ul class="feature-list">
              <li>✅ RESTful API 设计</li>
              <li>✅ Express 中间件架构</li>
              <li>✅ MySQL 数据库支持</li>
              <li>✅ 文件上传功能</li>
              <li>✅ CORS 跨域支持</li>
              <li>✅ 错误处理和日志</li>
              <li>✅ 环境变量配置</li>
            </ul>
          </el-col>
        </el-row>
      </el-card>

      <!-- 技术栈 -->
      <el-card class="demo-card">
        <div slot="header">
          <span>🛠️ 技术栈</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>前端技术</h4>
            <div class="tech-stack">
              <el-tag type="primary">Vue.js 2.x</el-tag>
              <el-tag type="success">ElementUI</el-tag>
              <el-tag type="info">Axios</el-tag>
              <el-tag type="warning">CSS3</el-tag>
              <el-tag>Webpack</el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <h4>后端技术</h4>
            <div class="tech-stack">
              <el-tag type="primary">Node.js</el-tag>
              <el-tag type="success">Express</el-tag>
              <el-tag type="info">MySQL</el-tag>
              <el-tag type="warning">Multer</el-tag>
              <el-tag>CORS</el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 项目结构 -->
      <el-card class="demo-card">
        <div slot="header">
          <span>📁 项目结构</span>
        </div>
        <pre style="background: #f5f5f5; padding: 15px; border-radius: 4px; overflow-x: auto;">
comic-reader/
├── backend/                 # 后端代码
│   ├── config/             # 数据库配置
│   ├── models/             # 数据模型
│   ├── routes/             # API路由
│   ├── middleware/         # 中间件
│   ├── uploads/            # 文件上传
│   └── app.js              # 应用入口
├── frontend/               # 前端代码
│   ├── index.html          # 漫画列表
│   ├── detail.html         # 漫画详情
│   ├── reader.html         # 阅读器
│   └── src/                # Vue组件
├── database/               # 数据库脚本
└── 开发指南.md             # 开发文档
        </pre>
      </el-card>

      <!-- API接口 -->
      <el-card class="demo-card">
        <div slot="header">
          <span>🔌 API接口</span>
        </div>
        <el-table :data="apiList" style="width: 100%">
          <el-table-column prop="method" label="方法" width="80">
            <template slot-scope="scope">
              <el-tag :type="getMethodType(scope.row.method)" size="mini">
                {{ scope.row.method }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="path" label="路径" width="200"></el-table-column>
          <el-table-column prop="description" label="说明"></el-table-column>
        </el-table>
      </el-card>

      <!-- 开始使用 -->
      <el-card class="demo-card">
        <div slot="header">
          <span>🎯 开始使用</span>
        </div>
        <el-steps :active="3" finish-status="success">
          <el-step title="安装依赖" description="npm install"></el-step>
          <el-step title="配置数据库" description="创建MySQL数据库"></el-step>
          <el-step title="启动服务" description="运行前后端服务"></el-step>
          <el-step title="开始体验" description="访问前端页面"></el-step>
        </el-steps>
        
        <div style="margin-top: 20px; text-align: center;">
          <el-button type="primary" size="large" @click="goToPage('index.html')">
            <i class="el-icon-right"></i> 立即体验
          </el-button>
        </div>
      </el-card>
    </div>
  </div>

  <!-- Vue.js -->
  <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
  <!-- ElementUI JS -->
  <script src="https://unpkg.com/element-ui/lib/index.js"></script>

  <script>
    new Vue({
      el: '#app',
      data: {
        apiList: [
          { method: 'GET', path: '/api/comics', description: '获取漫画列表' },
          { method: 'GET', path: '/api/comics/:id', description: '获取漫画详情' },
          { method: 'POST', path: '/api/comics', description: '创建漫画' },
          { method: 'GET', path: '/api/chapters/:id', description: '获取章节详情' },
          { method: 'GET', path: '/api/chapters/:id/pages', description: '获取章节页面' },
          { method: 'POST', path: '/api/upload/single', description: '单文件上传' },
          { method: 'POST', path: '/api/upload/multiple', description: '多文件上传' },
          { method: 'POST', path: '/api/upload/batch', description: '批量上传' }
        ]
      },
      methods: {
        goToPage(url) {
          window.location.href = url;
        },
        
        getMethodType(method) {
          const types = {
            'GET': 'success',
            'POST': 'primary',
            'PUT': 'warning',
            'DELETE': 'danger'
          };
          return types[method] || 'info';
        }
      }
    });
  </script>
</body>
</html>

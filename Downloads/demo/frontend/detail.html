<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>漫画详情 - 漫画阅读器</title>
  <!-- ElementUI CSS -->
  <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
      background-color: #f5f5f5;
    }
    .nav-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
      height: 60px;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    .detail-cover {
      width: 100%;
      max-width: 200px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    .comic-detail-info {
      padding: 20px 0;
    }
    .detail-title {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
      margin: 0 0 15px 0;
    }
    .detail-author {
      margin: 10px 0;
    }
    .detail-description {
      color: #606266;
      line-height: 1.6;
      margin: 15px 0;
    }
    .detail-stats {
      margin-top: 20px;
    }
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .chapter-button {
      width: 100%;
      text-align: left;
      margin-bottom: 10px;
    }
    .empty-chapters {
      text-align: center;
      color: #909399;
      padding: 40px 0;
    }
    .loading {
      text-align: center;
      padding: 40px;
    }
    .error {
      text-align: center;
      padding: 40px;
      color: #f56c6c;
    }
  </style>
</head>
<body>
  <div id="app">
    <!-- 导航栏 -->
    <div class="nav-header">
      <el-button @click="goBack" icon="el-icon-arrow-left" type="text" style="color: white;">
        返回
      </el-button>
      <span style="color: white; font-size: 18px;">漫画详情</span>
      <div></div>
    </div>

    <div class="container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <i class="el-icon-loading"></i>
        <p>加载中...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error">
        <i class="el-icon-warning"></i>
        <p>{{ error }}</p>
        <el-button @click="loadComicDetail" type="primary">重新加载</el-button>
      </div>

      <!-- 漫画详情 -->
      <div v-else-if="comic">
        <!-- 漫画信息 -->
        <el-card class="comic-info-card">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="8" :md="6">
              <img
                :src="comic.cover_image"
                :alt="comic.title"
                class="detail-cover"
                @error="handleImageError"
              />
            </el-col>
            <el-col :xs="24" :sm="16" :md="18">
              <div class="comic-detail-info">
                <h1 class="detail-title">{{ comic.title }}</h1>
                <p class="detail-author">
                  <el-tag type="info">{{ comic.author || '未知作者' }}</el-tag>
                </p>
                <p class="detail-description">{{ comic.description || '暂无描述' }}</p>
                <div class="detail-stats">
                  <el-tag>共 {{ chapters.length }} 章</el-tag>
                  <el-tag :type="comic.status === 'completed' ? 'success' : 'primary'" style="margin-left: 10px;">
                    {{ comic.status === 'completed' ? '已完结' : '连载中' }}
                  </el-tag>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 章节列表 -->
        <el-card class="chapters-card" style="margin-top: 20px;">
          <div slot="header" class="card-header">
            <span>章节列表</span>
            <el-button
              v-if="chapters.length > 0"
              @click="startReading(chapters[0].id)"
              type="primary"
              size="small"
            >
              开始阅读
            </el-button>
          </div>

          <div v-if="chapters.length > 0" class="chapters-list">
            <el-row :gutter="10">
              <el-col
                v-for="chapter in chapters"
                :key="chapter.id"
                :xs="12"
                :sm="8"
                :md="6"
                :lg="4"
              >
                <el-button
                  @click="startReading(chapter.id)"
                  class="chapter-button"
                  size="small"
                >
                  {{ chapter.title }}
                </el-button>
              </el-col>
            </el-row>
          </div>

          <div v-else class="empty-chapters">
            <p>暂无章节</p>
          </div>
        </el-card>
      </div>
    </div>
  </div>

  <!-- Vue.js -->
  <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
  <!-- ElementUI JS -->
  <script src="https://unpkg.com/element-ui/lib/index.js"></script>
  <!-- Axios -->
  <script src="https://unpkg.com/axios/dist/axios.min.js"></script>

  <script>
    new Vue({
      el: '#app',
      data: {
        comic: null,
        chapters: [],
        loading: false,
        error: null,
        comicId: null
      },
      created() {
        this.comicId = this.getComicIdFromUrl();
        this.loadComicDetail();
      },
      methods: {
        getComicIdFromUrl() {
          const urlParams = new URLSearchParams(window.location.search);
          return urlParams.get('id') || '1';
        },

        loadComicDetail() {
          this.loading = true;
          this.error = null;

          // 使用模拟数据
          setTimeout(() => {
            const mockComic = this.getMockComic(this.comicId);
            if (mockComic) {
              this.comic = mockComic;
              this.chapters = this.getMockChapters(this.comicId);
            } else {
              this.error = '漫画不存在';
            }
            this.loading = false;
          }, 800);
        },

        getMockComic(id) {
          const comics = {
            '1': {
              id: 1,
              title: "进击的巨人",
              description: "在这个世界上，人类居住在由三重巨大的城墙所围成的都市里。在城墙外面，有着似乎会捕食人类的巨人类生物，被称为「巨人」。主人公艾伦·耶格尔怀着对城墙外的憧憬，与青梅竹马的三笠·阿克曼、阿明·阿诺德一起加入了调查兵团。",
              author: "谏山创",
              cover_image: "https://picsum.photos/300/400?random=1",
              status: "completed"
            },
            '2': {
              id: 2,
              title: "鬼灭之刃",
              description: "大正时代，卖炭少年炭治郎的平凡生活在家人遭到鬼杀害后发生了变化。为了让变成鬼的妹妹祢豆子恢复人形，炭治郎踏上了成为鬼杀队队员的道路。这是一个关于兄妹之情、友情与成长的感人故事。",
              author: "吾峠呼世晴",
              cover_image: "https://picsum.photos/300/400?random=2",
              status: "completed"
            }
          };
          return comics[id];
        },

        getMockChapters(comicId) {
          const chapters = {
            '1': [
              { id: 1, title: "第1话：致两千年后的你", comic_id: 1 },
              { id: 2, title: "第2话：那一天", comic_id: 1 },
              { id: 3, title: "第3话：解散式之夜", comic_id: 1 },
              { id: 4, title: "第4话：初阵", comic_id: 1 },
              { id: 5, title: "第5话：在托洛斯特区攻防战", comic_id: 1 }
            ],
            '2': [
              { id: 6, title: "第1话：残酷", comic_id: 2 },
              { id: 7, title: "第2话：育手鬼", comic_id: 2 },
              { id: 8, title: "第3话：锖兔与真菰", comic_id: 2 },
              { id: 9, title: "第4话：最终选拔", comic_id: 2 }
            ]
          };
          return chapters[comicId] || [];
        },

        startReading(chapterId) {
          window.location.href = `reader.html?chapterId=${chapterId}`;
        },

        goBack() {
          window.location.href = 'index.html';
        },

        handleImageError(event) {
          event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNiIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD48L3N2Zz4=';
        }
      }
    });
  </script>
</body>
</html>
